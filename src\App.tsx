
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import Index from "./pages/Index";
import MainApp from "./pages/MainApp";
import NotFound from "./pages/NotFound";
import QuickAuthSetup from "./components/auth/QuickAuthSetup";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/app" element={<MainApp />} />
            <Route path="/auth/setup" element={<QuickAuthSetup />} />
            {/* Legacy routes redirect to main app */}
            <Route path="/dashboard" element={<MainApp />} />
            <Route path="/mobile" element={<MainApp />} />
            <Route path="/map" element={<MainApp />} />
            <Route path="/route-planning" element={<MainApp />} />
            <Route path="/report" element={<MainApp />} />
            <Route path="/analytics" element={<MainApp />} />
            <Route path="/sos" element={<MainApp />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
