/**
 * MainApp - Unified Navigation Application
 * All-in-one interface combining dashboard, map, navigation, and SOS features
 */

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import InteractiveMap, { RoadIssue } from "@/components/InteractiveMap";
import { dataService } from "@/services/dataService";
import { useAuth } from "@/contexts/AuthContext";
import {
  MapPin,
  Navigation,
  AlertTriangle,
  Settings,
  User,
  Menu,
  X,
  Route,
  Shield,
  BarChart3,
  Plus,
  Search,
  Filter,
  Layers,
  Phone,
  Car,
  Clock,
  Star,
  Bell,
  MessageSquare,
  Zap,
  Target,
  Compass,
  Activity,
  TrendingUp,
  Users,
  Eye,
  Heart,
  Share2,
  Bookmark,
  MoreVertical,
  ChevronRight,
  ChevronDown,
  Wifi,
  Battery,
  Signal,
  CheckCircle
} from "lucide-react";

type AppMode = 'dashboard' | 'navigation' | 'report' | 'sos' | 'analytics';
type MapTheme = 'standard' | 'satellite' | 'dark' | 'terrain';

const MainApp = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  
  // Core app state
  const [currentMode, setCurrentMode] = useState<AppMode>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mapTheme, setMapTheme] = useState<MapTheme>('standard');
  
  // Data state
  const [allIssues, setAllIssues] = useState<RoadIssue[]>([]);
  const [filteredIssues, setFilteredIssues] = useState<RoadIssue[]>([]);
  const [selectedIssue, setSelectedIssue] = useState<RoadIssue | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter state
  const [filterType, setFilterType] = useState("all");
  const [filterSeverity, setFilterSeverity] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [showLayers, setShowLayers] = useState(false);
  const [visibleLayers, setVisibleLayers] = useState({
    potholes: true,
    construction: true,
    accidents: true,
    police: true,
    cameras: true,
    sos: true,
    traffic: false,
  });

  // Navigation state
  const [routeStart, setRouteStart] = useState("");
  const [routeEnd, setRouteEnd] = useState("");
  const [isNavigating, setIsNavigating] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{lat: number, lng: number} | null>(null);
  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  const [routeData, setRouteData] = useState<any>(null);
  const [navigationStep, setNavigationStep] = useState(0);
  const [calculatingRoute, setCalculatingRoute] = useState(false);
  const [navigationError, setNavigationError] = useState<string | null>(null);

  // Location search state
  const [startSuggestions, setStartSuggestions] = useState<any[]>([]);
  const [endSuggestions, setEndSuggestions] = useState<any[]>([]);
  const [showStartSuggestions, setShowStartSuggestions] = useState(false);
  const [showEndSuggestions, setShowEndSuggestions] = useState(false);
  const [searchingLocation, setSearchingLocation] = useState(false);

  // Load data and initialize location services on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🚀 Loading RoadPulse data...');
        setError(null);

        await dataService.initialize();
        const data = await dataService.getIssues();

        console.log(`✅ Loaded ${data.length} issues from Supabase`);
        setAllIssues(data);
        setFilteredIssues(data);
      } catch (error) {
        console.error('❌ Failed to load data:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to load data';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    const initializeLocation = async () => {
      try {
        console.log('📍 Initializing location services...');
        const permission = await navigator.permissions.query({name: 'geolocation'});
        setLocationPermission(permission.state as 'granted' | 'denied' | 'prompt');

        if (permission.state === 'granted') {
          getCurrentLocation();
        }
      } catch (error) {
        console.log('📍 Location permission check not supported');
      }
    };

    loadData();
    initializeLocation();
  }, []);

  // Filter issues when filters change
  useEffect(() => {
    let filtered = [...allIssues];

    // Filter by layer visibility
    filtered = filtered.filter(issue => {
      switch (issue.type) {
        case 'pothole': return visibleLayers.potholes;
        case 'construction': return visibleLayers.construction;
        case 'accident': return visibleLayers.accidents;
        case 'police': return visibleLayers.police;
        case 'camera': return visibleLayers.cameras;
        case 'sos': return visibleLayers.sos;
        default: return true;
      }
    });

    // Filter by type
    if (filterType !== "all") {
      filtered = filtered.filter(issue => issue.type === filterType);
    }

    // Filter by severity
    if (filterSeverity !== "all") {
      filtered = filtered.filter(issue => issue.severity === filterSeverity);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(issue => 
        issue.location.toLowerCase().includes(query) ||
        issue.description.toLowerCase().includes(query) ||
        issue.reportedBy.toLowerCase().includes(query)
      );
    }

    setFilteredIssues(filtered);
  }, [allIssues, filterType, filterSeverity, searchQuery, visibleLayers]);

  // Location services
  const getCurrentLocation = () => {
    setSearchingLocation(true);

    if (!navigator.geolocation) {
      console.error('❌ Geolocation not supported');
      setLocationPermission('denied');
      setSearchingLocation(false);
      // Use Harare CBD as fallback
      setCurrentLocation({ lat: -17.8292, lng: 31.0522 });
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        console.log('📍 Raw GPS coordinates:', latitude, longitude);

        // Validate coordinates are in Zimbabwe region
        if (latitude < -22.5 || latitude > -15.5 || longitude < 25 || longitude > 33) {
          console.warn('⚠️ GPS coordinates outside Zimbabwe, using Harare CBD as fallback');
          setCurrentLocation({ lat: -17.8292, lng: 31.0522 });
        } else {
          setCurrentLocation({ lat: latitude, lng: longitude });
          console.log('✅ Valid Zimbabwe coordinates:', latitude, longitude);
        }

        setLocationPermission('granted');
        setSearchingLocation(false);
      },
      (error) => {
        console.error('❌ Location error:', error.message);
        setLocationPermission('denied');
        setSearchingLocation(false);

        // Use Harare CBD as fallback for demo purposes
        console.log('🏢 Using Harare CBD as fallback location');
        setCurrentLocation({ lat: -17.8292, lng: 31.0522 });
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 60000 // Cache for 1 minute
      }
    );
  };

  // Zimbabwe locations database with real coordinates
  const zimbabweLocations = {
    // Major Cities
    'harare': { name: 'Harare', coordinates: [31.0522, -17.8292], type: 'city' },
    'harare cbd': { name: 'Harare CBD', coordinates: [31.0522, -17.8292], type: 'district' },
    'bulawayo': { name: 'Bulawayo', coordinates: [28.5833, -20.1500], type: 'city' },
    'chitungwiza': { name: 'Chitungwiza', coordinates: [31.0833, -18.0167], type: 'city' },
    'mutare': { name: 'Mutare', coordinates: [32.6667, -18.9667], type: 'city' },
    'gweru': { name: 'Gweru', coordinates: [29.8167, -19.4500], type: 'city' },
    'kwekwe': { name: 'Kwekwe', coordinates: [29.8167, -18.9167], type: 'city' },
    'kadoma': { name: 'Kadoma', coordinates: [29.9167, -18.3333], type: 'city' },
    'masvingo': { name: 'Masvingo', coordinates: [30.8333, -20.0667], type: 'city' },

    // Harare Suburbs & Districts
    'avondale': { name: 'Avondale, Harare', coordinates: [31.0167, -17.8000], type: 'suburb' },
    'borrowdale': { name: 'Borrowdale, Harare', coordinates: [31.0833, -17.7833], type: 'suburb' },
    'mount pleasant': { name: 'Mount Pleasant, Harare', coordinates: [31.0667, -17.7833], type: 'suburb' },
    'newlands': { name: 'Newlands, Harare', coordinates: [31.0333, -17.7667], type: 'suburb' },
    'highlands': { name: 'Highlands, Harare', coordinates: [31.0500, -17.7500], type: 'suburb' },
    'glen view': { name: 'Glen View, Harare', coordinates: [30.9833, -17.8500], type: 'suburb' },
    'warren park': { name: 'Warren Park, Harare', coordinates: [30.9667, -17.8333], type: 'suburb' },
    'mbare': { name: 'Mbare, Harare', coordinates: [31.0333, -17.8667], type: 'suburb' },
    'eastlea': { name: 'Eastlea, Harare', coordinates: [31.0833, -17.8167], type: 'suburb' },
    'belvedere': { name: 'Belvedere, Harare', coordinates: [31.0167, -17.8333], type: 'suburb' },

    // Major Landmarks & Institutions
    'university of zimbabwe': { name: 'University of Zimbabwe', coordinates: [31.0333, -17.7833], type: 'university' },
    'harare international airport': { name: 'Robert Gabriel Mugabe International Airport', coordinates: [31.1333, -17.9167], type: 'airport' },
    'airport': { name: 'Robert Gabriel Mugabe International Airport', coordinates: [31.1333, -17.9167], type: 'airport' },
    'parirenyatwa hospital': { name: 'Parirenyatwa Hospital', coordinates: [31.0500, -17.8000], type: 'hospital' },
    'sally mugabe hospital': { name: 'Sally Mugabe Hospital', coordinates: [31.0333, -17.8333], type: 'hospital' },

    // Shopping Centers
    'eastgate shopping centre': { name: 'Eastgate Shopping Centre', coordinates: [31.0833, -17.8167], type: 'mall' },
    'eastgate': { name: 'Eastgate Shopping Centre', coordinates: [31.0833, -17.8167], type: 'mall' },
    'westgate shopping centre': { name: 'Westgate Shopping Centre', coordinates: [30.9833, -17.8000], type: 'mall' },
    'westgate': { name: 'Westgate Shopping Centre', coordinates: [30.9833, -17.8000], type: 'mall' },
    'sam levy village': { name: 'Sam Levy Village', coordinates: [31.0833, -17.7833], type: 'mall' },
    'avondale shopping centre': { name: 'Avondale Shopping Centre', coordinates: [31.0167, -17.8000], type: 'mall' },
    'newlands shopping centre': { name: 'Newlands Shopping Centre', coordinates: [31.0333, -17.7667], type: 'mall' },

    // Major Roads & Intersections
    'samora machel avenue': { name: 'Samora Machel Avenue', coordinates: [31.0522, -17.8292], type: 'road' },
    'robert mugabe road': { name: 'Robert Mugabe Road', coordinates: [31.0400, -17.8200], type: 'road' },
    'enterprise road': { name: 'Enterprise Road', coordinates: [31.0600, -17.8100], type: 'road' },
    'leopold takawira street': { name: 'Leopold Takawira Street', coordinates: [31.0522, -17.8250], type: 'road' },

    // Government Buildings
    'parliament of zimbabwe': { name: 'Parliament of Zimbabwe', coordinates: [31.0522, -17.8200], type: 'government' },
    'state house': { name: 'State House', coordinates: [31.0400, -17.8100], type: 'government' },
    'reserve bank of zimbabwe': { name: 'Reserve Bank of Zimbabwe', coordinates: [31.0522, -17.8292], type: 'bank' },

    // Sports Venues
    'national sports stadium': { name: 'National Sports Stadium', coordinates: [31.0167, -17.8500], type: 'stadium' },
    'harare sports club': { name: 'Harare Sports Club', coordinates: [31.0522, -17.8200], type: 'sports' },

    // Hotels
    'meikles hotel': { name: 'Meikles Hotel', coordinates: [31.0522, -17.8292], type: 'hotel' },
    'rainbow towers': { name: 'Rainbow Towers Hotel', coordinates: [31.0522, -17.8250], type: 'hotel' },

    // Other Major Cities Detail
    'victoria falls': { name: 'Victoria Falls', coordinates: [25.8333, -17.9333], type: 'tourist' },
    'hwange': { name: 'Hwange', coordinates: [26.5000, -18.3667], type: 'city' },
    'kariba': { name: 'Kariba', coordinates: [28.8000, -16.5167], type: 'city' },
    'chinhoyi': { name: 'Chinhoyi', coordinates: [30.2000, -17.3667], type: 'city' },
    'norton': { name: 'Norton', coordinates: [30.7000, -17.8833], type: 'city' },
    'ruwa': { name: 'Ruwa', coordinates: [31.2500, -17.8833], type: 'city' }
  };

  // Location search with Zimbabwe database fallback
  const searchLocations = async (query: string): Promise<any[]> => {
    if (!query || query.length < 2) return [];

    const normalizedQuery = query.toLowerCase().trim();
    console.log('🔍 Searching for:', normalizedQuery);

    // First, search in our Zimbabwe database
    const localResults = Object.entries(zimbabweLocations)
      .filter(([key, location]) =>
        key.includes(normalizedQuery) ||
        location.name.toLowerCase().includes(normalizedQuery)
      )
      .map(([key, location]) => ({
        id: key,
        name: location.name,
        address: location.name,
        coordinates: location.coordinates, // [lng, lat]
        confidence: 1.0,
        type: location.type,
        source: 'local'
      }))
      .slice(0, 5);

    console.log('🏠 Local Zimbabwe results:', localResults);

    // If we have good local results, return them
    if (localResults.length > 0) {
      return localResults;
    }

    // Fallback to API search for more specific addresses
    try {
      const API_KEY = '5b3ce3597851110001cf62481c97cfb4ade84ed285e0af63bf2228f4';

      const params = new URLSearchParams({
        api_key: API_KEY,
        text: `${query}, Zimbabwe`,
        'boundary.country': 'ZW',
        'focus.point.lat': '-17.8292',
        'focus.point.lon': '31.0522',
        size: '5',
        layers: 'address,venue,street,locality'
      });

      const response = await fetch(
        `https://api.openrouteservice.org/geocode/search?${params}`
      );

      if (!response.ok) {
        console.error('❌ Search API error:', response.status);
        return localResults;
      }

      const data = await response.json();
      console.log('🌐 API search results:', data);

      const apiResults = data.features.map((feature: any) => ({
        id: feature.properties.id,
        name: feature.properties.label || feature.properties.name,
        address: feature.properties.name || feature.properties.label,
        coordinates: feature.geometry.coordinates, // [lng, lat]
        confidence: feature.properties.confidence || 0.5,
        locality: feature.properties.locality,
        region: feature.properties.region,
        source: 'api'
      })).filter((result: any) => result.confidence > 0.3);

      // Combine local and API results, prioritizing local
      return [...localResults, ...apiResults].slice(0, 8);

    } catch (error) {
      console.error('❌ Location search error:', error);
      return localResults;
    }
  };

  // Get route using OpenRouteService Directions API
  const getRoute = async (start: [number, number], end: [number, number]) => {
    try {
      console.log('🧭 Calculating route from:', start, 'to:', end);
      const API_KEY = '5b3ce3597851110001cf62481c97cfb4ade84ed285e0af63bf2228f4';

      // Validate coordinates
      if (!start || !end || start.length !== 2 || end.length !== 2) {
        throw new Error('Invalid coordinates provided');
      }

      // Ensure coordinates are numbers and in valid range
      const [startLng, startLat] = start.map(Number);
      const [endLng, endLat] = end.map(Number);

      if (isNaN(startLng) || isNaN(startLat) || isNaN(endLng) || isNaN(endLat)) {
        throw new Error('Coordinates must be valid numbers');
      }

      if (Math.abs(startLat) > 90 || Math.abs(endLat) > 90 || Math.abs(startLng) > 180 || Math.abs(endLng) > 180) {
        throw new Error('Coordinates out of valid range');
      }

      // Validate coordinates are in Zimbabwe
      if (!isValidZimbabweCoordinates(startLat, startLng)) {
        console.warn('⚠️ Start coordinates outside Zimbabwe:', startLat, startLng);
      }
      if (!isValidZimbabweCoordinates(endLat, endLng)) {
        console.warn('⚠️ End coordinates outside Zimbabwe:', endLat, endLng);
      }

      const requestBody = {
        coordinates: [[startLng, startLat], [endLng, endLat]], // Ensure proper format
        format: 'json',
        instructions: true,
        geometry: true,
        elevation: false
      };

      console.log('📡 Sending route request:', requestBody);

      const response = await fetch(
        `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(requestBody)
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Route API error:', response.status, errorText);

        // Try to parse error details
        try {
          const errorData = JSON.parse(errorText);
          console.error('❌ API Error details:', errorData);

          // Handle specific API errors
          if (errorData.error?.code === 2003) {
            throw new Error('Route service temporarily unavailable. Using fallback route calculation.');
          } else if (errorData.error?.code === 2004) {
            throw new Error('No route found between these locations. Try different addresses.');
          } else {
            throw new Error(`Route calculation failed: ${errorData.error?.message || 'Service unavailable'}`);
          }
        } catch (parseError) {
          throw new Error('Route service temporarily unavailable. Using fallback route calculation.');
        }
      }

      const data = await response.json();
      console.log('✅ Route calculated successfully:', data);

      if (!data.routes || data.routes.length === 0) {
        throw new Error('No route found between these locations');
      }

      return data.routes[0];
    } catch (error) {
      console.error('❌ Route calculation error:', error);

      // Return a fallback route with basic info
      const distance = calculateStraightLineDistance(start, end);
      const estimatedTime = Math.round(distance * 3); // More realistic estimate: 3 minutes per km in city traffic

      console.log('🔄 Using fallback route calculation');

      return {
        summary: {
          distance: distance * 1000, // Convert to meters
          duration: estimatedTime * 60 // Convert to seconds
        },
        geometry: null, // Will trigger fallback straight line display
        segments: [{
          steps: [
            {
              instruction: `Head towards destination`,
              distance: distance * 1000,
              duration: estimatedTime * 60,
              maneuver: {
                location: start
              }
            },
            {
              instruction: `Arrive at destination`,
              distance: 0,
              duration: 0,
              maneuver: {
                location: end
              }
            }
          ]
        }],
        fallback: true,
        fallbackMessage: 'Using estimated route - actual route may differ'
      };
    }
  };

  // Validate if coordinates are within Zimbabwe bounds
  const isValidZimbabweCoordinates = (lat: number, lng: number): boolean => {
    // Zimbabwe approximate bounds
    const zimbabweBounds = {
      north: -15.5,
      south: -22.5,
      east: 33.0,
      west: 25.0
    };

    return lat >= zimbabweBounds.south && lat <= zimbabweBounds.north &&
           lng >= zimbabweBounds.west && lng <= zimbabweBounds.east;
  };

  // Calculate straight-line distance between two points (Haversine formula)
  const calculateStraightLineDistance = (start: [number, number], end: [number, number]): number => {
    const [lng1, lat1] = start;
    const [lng2, lat2] = end;

    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Event handlers
  const handleModeChange = (mode: AppMode) => {
    console.log('🔄 Switching to mode:', mode);
    setCurrentMode(mode);
    setSelectedIssue(null);
  };

  const handleIssueClick = (issue: RoadIssue) => {
    setSelectedIssue(issue);
    setCurrentMode('dashboard'); // Switch to dashboard to show details
  };

  const handleLayerToggle = (layerType: keyof typeof visibleLayers) => {
    setVisibleLayers(prev => ({
      ...prev,
      [layerType]: !prev[layerType]
    }));
  };

  const handleStartNavigation = async () => {
    if (!routeStart || !routeEnd) return;

    try {
      console.log(`🧭 Starting navigation from ${routeStart} to ${routeEnd}`);
      setCalculatingRoute(true);
      setNavigationError(null);
      setCurrentMode('navigation');

      // Get coordinates for start and end locations
      let startCoords: [number, number];
      let endCoords: [number, number];

      // Handle current location
      if (routeStart === "Current Location" && currentLocation) {
        startCoords = [currentLocation.lng, currentLocation.lat];
        console.log('📍 Using current location as start:', startCoords);
      } else {
        console.log('🔍 Searching for start location:', routeStart);
        const startResults = await searchLocations(routeStart);
        if (startResults.length === 0) {
          // Fallback: try Zimbabwe locations database
          const fallbackKey = routeStart.toLowerCase();
          if (zimbabweLocations[fallbackKey as keyof typeof zimbabweLocations]) {
            const location = zimbabweLocations[fallbackKey as keyof typeof zimbabweLocations];
            startCoords = location.coordinates;
            console.log('📍 Using Zimbabwe database coordinates for start:', startCoords, location.name);
          } else {
            throw new Error(`Start location "${routeStart}" not found. Try "Harare CBD", "Airport", "University of Zimbabwe", or "Eastgate".`);
          }
        } else {
          startCoords = startResults[0].coordinates;
          console.log('📍 Found start location:', startCoords);
        }
      }

      console.log('🔍 Searching for end location:', routeEnd);
      const endResults = await searchLocations(routeEnd);
      if (endResults.length === 0) {
        // Fallback: try Zimbabwe locations database
        const fallbackKey = routeEnd.toLowerCase();
        if (zimbabweLocations[fallbackKey as keyof typeof zimbabweLocations]) {
          const location = zimbabweLocations[fallbackKey as keyof typeof zimbabweLocations];
          endCoords = location.coordinates;
          console.log('📍 Using Zimbabwe database coordinates for end:', endCoords, location.name);
        } else {
          throw new Error(`Destination "${routeEnd}" not found. Try "Harare CBD", "Airport", "University of Zimbabwe", "Eastgate", or "Avondale".`);
        }
      } else {
        endCoords = endResults[0].coordinates;
        console.log('📍 Found end location:', endCoords);
      }

      // Calculate route
      const route = await getRoute(startCoords, endCoords);

      // Add start and end coordinates to route data for map display
      const enhancedRoute = {
        ...route,
        startCoordinates: startCoords,
        endCoordinates: endCoords,
        startLocation: routeStart,
        endLocation: routeEnd
      };

      setRouteData(enhancedRoute);
      setNavigationStep(0);
      setIsNavigating(true);

      console.log('✅ Route calculated successfully:', enhancedRoute);
    } catch (error) {
      console.error('❌ Navigation start failed:', error);
      setNavigationError(error instanceof Error ? error.message : 'Navigation failed');
      setIsNavigating(false);
    } finally {
      setCalculatingRoute(false);
    }
  };

  const handleSOSRequest = () => {
    setCurrentMode('sos');
    console.log('🆘 SOS mode activated');
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Location search handlers
  const handleStartLocationSearch = async (query: string) => {
    setRouteStart(query);
    setNavigationError(null); // Clear error when user types
    if (query.length >= 3) {
      const suggestions = await searchLocations(query);
      setStartSuggestions(suggestions);
      setShowStartSuggestions(true);
    } else {
      setShowStartSuggestions(false);
    }
  };

  const handleEndLocationSearch = async (query: string) => {
    setRouteEnd(query);
    setNavigationError(null); // Clear error when user types
    if (query.length >= 3) {
      const suggestions = await searchLocations(query);
      setEndSuggestions(suggestions);
      setShowEndSuggestions(true);
    } else {
      setShowEndSuggestions(false);
    }
  };

  const selectStartLocation = (location: any) => {
    setRouteStart(location.name);
    setShowStartSuggestions(false);
  };

  const selectEndLocation = (location: any) => {
    setRouteEnd(location.name);
    setShowEndSuggestions(false);
  };

  const useCurrentLocation = () => {
    if (currentLocation) {
      setRouteStart("Current Location");
      setShowStartSuggestions(false);
    } else {
      getCurrentLocation();
    }
  };

  // Handle authentication redirect
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="h-screen flex bg-gradient-to-br from-slate-50 to-orange-50">
      {/* Advanced Sidebar - Mobile Responsive */}
      <div className={`
        ${sidebarOpen ? 'w-80 md:w-80' : 'w-0 md:w-20'}
        ${sidebarOpen ? 'fixed md:relative' : 'hidden md:flex'}
        transition-all duration-500 ease-in-out bg-white/95 backdrop-blur-xl
        border-r border-gray-200/50 shadow-xl flex flex-col relative z-50
        ${sidebarOpen ? 'inset-y-0 left-0' : ''}
      `}>
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-blue-600/5 to-orange-600/5 pointer-events-none" />

        {/* Header - Mobile Responsive */}
        <div className="relative p-4 md:p-6 border-b border-gray-200/50">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-blue-600 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <MapPin className="w-5 h-5 md:w-6 md:h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-lg md:text-xl font-bold bg-gradient-to-r from-blue-600 to-orange-600 bg-clip-text text-transparent">
                    RoadPulse
                  </h1>
                  <p className="text-xs md:text-sm text-gray-500 font-medium">Smart Navigation</p>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="hover:bg-orange-50 rounded-xl transition-all duration-300"
            >
              {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>

          {/* Status Bar - Mobile Responsive */}
          {sidebarOpen && (
            <div className="mt-3 md:mt-4 flex items-center gap-2 text-xs flex-wrap">
              <div className="flex items-center gap-1 text-green-600">
                <Wifi className="w-3 h-3" />
                <span className="hidden sm:inline">Online</span>
              </div>
              <div className="w-1 h-1 bg-gray-300 rounded-full hidden sm:block" />
              <div className="flex items-center gap-1 text-blue-600">
                <Activity className="w-3 h-3" />
                <span className="hidden sm:inline">Live Data</span>
              </div>
              <div className="w-1 h-1 bg-gray-300 rounded-full hidden sm:block" />
              <div className="flex items-center gap-1 text-orange-600">
                <Signal className="w-3 h-3" />
                <span className="hidden sm:inline">GPS Active</span>
              </div>
            </div>
          )}
        </div>

        {/* Advanced Navigation Menu - Mobile Responsive */}
        <div className="flex-1 p-3 md:p-4 relative">
          <div className="space-y-2 md:space-y-3">
            {/* Primary Actions */}
            <div className="space-y-2">
              <NavigationButton
                icon={BarChart3}
                label="Dashboard"
                isActive={currentMode === 'dashboard'}
                onClick={() => handleModeChange('dashboard')}
                sidebarOpen={sidebarOpen}
                badge={filteredIssues.length}
                gradient="from-blue-500 to-orange-500"
              />

              <NavigationButton
                icon={Navigation}
                label="Navigation"
                isActive={currentMode === 'navigation'}
                onClick={() => handleModeChange('navigation')}
                sidebarOpen={sidebarOpen}
                badge={isNavigating ? "ACTIVE" : undefined}
                gradient="from-green-500 to-emerald-600"
              />

              <NavigationButton
                icon={Plus}
                label="Report Issue"
                isActive={currentMode === 'report'}
                onClick={() => handleModeChange('report')}
                sidebarOpen={sidebarOpen}
                gradient="from-orange-500 to-amber-600"
              />
            </div>

            {/* Divider */}
            {sidebarOpen && <div className="border-t border-gray-200/50 my-3 md:my-4" />}

            {/* Secondary Actions */}
            <div className="space-y-2">
              <NavigationButton
                icon={Shield}
                label="SOS Emergency"
                isActive={currentMode === 'sos'}
                onClick={() => handleModeChange('sos')}
                sidebarOpen={sidebarOpen}
                badge={allIssues.filter(i => i.type === 'sos').length}
                gradient="from-red-500 to-red-600"
                isEmergency={true}
              />

              <NavigationButton
                icon={BarChart3}
                label="Analytics"
                isActive={currentMode === 'analytics'}
                onClick={() => handleModeChange('analytics')}
                sidebarOpen={sidebarOpen}
                gradient="from-orange-500 to-orange-600"
              />
            </div>
          </div>

          {sidebarOpen && (
            <>
              {/* Advanced Stats Dashboard - Mobile Responsive */}
              <div className="mt-4 md:mt-6 p-3 md:p-4 bg-gradient-to-br from-blue-50 to-orange-50 rounded-2xl border border-orange-200/50">
                <div className="flex items-center gap-2 mb-3">
                  <Activity className="w-4 h-4 text-orange-600" />
                  <h3 className="text-sm font-semibold text-gray-800">Live Insights</h3>
                </div>
                <div className="grid grid-cols-2 gap-2 md:gap-3">
                  <StatCard
                    icon={Target}
                    label="Total"
                    value={allIssues.length}
                    color="blue"
                  />
                  <StatCard
                    icon={Eye}
                    label="Visible"
                    value={filteredIssues.length}
                    color="green"
                  />
                  <StatCard
                    icon={Shield}
                    label="SOS"
                    value={allIssues.filter(i => i.type === 'sos').length}
                    color="red"
                  />
                  <StatCard
                    icon={TrendingUp}
                    label="Active"
                    value={allIssues.filter(i => i.status === 'active').length}
                    color="orange"
                  />
                </div>
              </div>

              {/* Advanced User Profile - Mobile Responsive */}
              <div className="mt-4 md:mt-6 p-3 md:p-4 bg-gradient-to-br from-gray-50 to-orange-50 rounded-2xl border border-gray-200/50">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-blue-500 to-orange-500 rounded-xl flex items-center justify-center">
                    <User className="w-4 h-4 md:w-5 md:h-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-800 truncate">{user?.name || 'Demo User'}</p>
                    <p className="text-xs text-gray-500 truncate">{user?.email || '<EMAIL>'}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="hover:bg-orange-100/50 flex-shrink-0"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center gap-2 mb-3 flex-wrap">
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span>Online</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-orange-600">
                    <Star className="w-3 h-3 fill-current" />
                    <span>4.8 Rating</span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-all duration-300 text-sm"
                  onClick={handleLogout}
                >
                  <span>Sign Out</span>
                </Button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Main Content - Mobile Responsive */}
      <div className="flex-1 flex flex-col relative">
        {/* Mobile Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Advanced Top Bar - Mobile Responsive */}
        <div className="h-16 md:h-20 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-sm flex items-center justify-between px-4 md:px-8 relative z-30">
          <div className="flex items-center gap-2 md:gap-6 flex-1">
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="md:hidden hover:bg-orange-50 rounded-xl"
            >
              <Menu className="w-5 h-5" />
            </Button>

            <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
              <div className={`w-2 h-2 md:w-3 md:h-3 rounded-full ${currentMode === 'sos' ? 'bg-red-500 animate-pulse' : 'bg-green-500'}`} />
              <h2 className="text-lg md:text-2xl font-bold text-gray-900 truncate">
                {currentMode === 'sos' ? (
                  <span className="text-red-600">🆘 Emergency</span>
                ) : (
                  <span className="capitalize">{currentMode}</span>
                )}
              </h2>
            </div>

            <div className="hidden md:flex items-center gap-2">
              <Badge
                variant="outline"
                className="bg-gradient-to-r from-blue-50 to-orange-50 border-orange-200 text-orange-700 font-medium"
              >
                {mapTheme === 'standard' && '🗺️ Standard'}
                {mapTheme === 'satellite' && '🛰️ Satellite'}
                {mapTheme === 'dark' && '🌙 Dark'}
                {mapTheme === 'terrain' && '🏔️ Terrain'}
              </Badge>

              <Badge variant="outline" className="bg-green-50 border-green-200 text-green-700">
                <Activity className="w-3 h-3 mr-1" />
                Live
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-1 md:gap-3">
            {/* Notifications - Hidden on mobile */}
            <Button
              variant="ghost"
              size="sm"
              className="relative hover:bg-orange-50 rounded-xl transition-all duration-300 hidden md:flex"
            >
              <Bell className="w-5 h-5" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">3</span>
              </div>
            </Button>

            {/* Layers Control - Responsive */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowLayers(!showLayers)}
              className={`hover:bg-orange-50 border-orange-200 transition-all duration-300 ${
                showLayers ? "bg-orange-100 border-orange-300 text-orange-700" : ""
              }`}
            >
              <Layers className="w-4 h-4 md:mr-2" />
              <span className="hidden md:inline">Layers</span>
              <ChevronDown className={`w-4 h-4 ml-1 transition-transform duration-300 ${showLayers ? 'rotate-180' : ''} hidden md:block`} />
            </Button>

            {/* Settings - Hidden on mobile */}
            <Button
              variant="outline"
              size="sm"
              className="hover:bg-orange-50 border-orange-200 hover:text-orange-700 transition-all duration-300 hidden md:flex"
            >
              <Settings className="w-4 h-4" />
            </Button>

            {/* Quick Actions - Responsive */}
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-500 to-orange-500 hover:from-blue-600 hover:to-orange-600 text-white shadow-lg"
                onClick={() => handleModeChange('navigation')}
              >
                <Navigation className="w-4 h-4 md:mr-1" />
                <span className="hidden md:inline">Navigate</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Layers Panel - Mobile Responsive */}
        {showLayers && (
          <div className="bg-white border-b border-gray-200 p-4 shadow-sm relative z-20">
            <div className="max-w-6xl mx-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Map Layers & Themes</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLayers(false)}
                  className="md:hidden hover:bg-gray-100"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Layer toggles */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Incidents</h4>
                  <div className="space-y-1 max-h-40 md:max-h-none overflow-y-auto">
                    {Object.entries(visibleLayers).map(([key, value]) => (
                      <label key={key} className="flex items-center text-sm cursor-pointer">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={() => handleLayerToggle(key as keyof typeof visibleLayers)}
                          className="mr-2"
                        />
                        <span className="capitalize">{key} ({allIssues.filter(i => i.type === key).length})</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Map themes */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Map Style</h4>
                  <div className="space-y-1">
                    {(['standard', 'satellite', 'dark', 'terrain'] as MapTheme[]).map((theme) => (
                      <label key={theme} className="flex items-center text-sm cursor-pointer">
                        <input
                          type="radio"
                          name="mapTheme"
                          checked={mapTheme === theme}
                          onChange={() => setMapTheme(theme)}
                          className="mr-2"
                        />
                        <span className="capitalize">{theme}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Area - Mobile Responsive */}
        <div className="flex-1 flex flex-col md:flex-row">
          {/* Map */}
          <div className="flex-1 relative">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600">Loading RoadPulse...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full bg-red-50">
                <div className="text-center p-4 md:p-8">
                  <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
                  <h3 className="text-lg font-semibold text-red-700 mb-2">Error</h3>
                  <p className="text-red-600 mb-4 text-sm md:text-base">{error}</p>
                  <Button onClick={() => window.location.reload()}>Retry</Button>
                </div>
              </div>
            ) : (
              <InteractiveMap
                height="100%"
                center={isNavigating && routeData ?
                  (currentLocation ? [currentLocation.lat, currentLocation.lng] : [-17.8292, 31.0522]) :
                  (currentLocation ? [currentLocation.lat, currentLocation.lng] : [-17.8292, 31.0522])
                }
                zoom={isNavigating ? 15 : (currentLocation ? 14 : 12)}
                issues={filteredIssues}
                showControls={true}
                onIssueClick={handleIssueClick}
                mapTheme={mapTheme}
                routeData={routeData}
                isNavigating={isNavigating}
                currentLocation={currentLocation}
                navigationStep={navigationStep}
              />
            )}
          </div>

          {/* Right Sidebar - Mode-specific content - Mobile Responsive */}
          <div className="w-full md:w-96 bg-white border-t md:border-t-0 md:border-l border-gray-200 flex flex-col max-h-96 md:max-h-none">
            {currentMode === 'dashboard' && (
              <DashboardPanel
                issues={filteredIssues}
                selectedIssue={selectedIssue}
                onIssueSelect={setSelectedIssue}
                filterType={filterType}
                setFilterType={setFilterType}
                filterSeverity={filterSeverity}
                setFilterSeverity={setFilterSeverity}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
              />
            )}

            {currentMode === 'navigation' && (
              <NavigationPanel
                routeStart={routeStart}
                setRouteStart={setRouteStart}
                routeEnd={routeEnd}
                setRouteEnd={setRouteEnd}
                isNavigating={isNavigating}
                onStartNavigation={handleStartNavigation}
                currentLocation={currentLocation}
                locationPermission={locationPermission}
                searchingLocation={searchingLocation}
                startSuggestions={startSuggestions}
                endSuggestions={endSuggestions}
                showStartSuggestions={showStartSuggestions}
                showEndSuggestions={showEndSuggestions}
                onStartLocationSearch={handleStartLocationSearch}
                onEndLocationSearch={handleEndLocationSearch}
                onSelectStartLocation={selectStartLocation}
                onSelectEndLocation={selectEndLocation}
                onUseCurrentLocation={useCurrentLocation}
                routeData={routeData}
                navigationStep={navigationStep}
                calculatingRoute={calculatingRoute}
                navigationError={navigationError}
              />
            )}

            {currentMode === 'report' && (
              <ReportPanel />
            )}

            {currentMode === 'sos' && (
              <SOSPanel onSOSRequest={handleSOSRequest} />
            )}

            {currentMode === 'analytics' && (
              <AnalyticsPanel issues={allIssues} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Advanced Dashboard Panel Component
const DashboardPanel = ({
  issues,
  selectedIssue,
  onIssueSelect,
  filterType,
  setFilterType,
  filterSeverity,
  setFilterSeverity,
  searchQuery,
  setSearchQuery
}: {
  issues: RoadIssue[];
  selectedIssue: RoadIssue | null;
  onIssueSelect: (issue: RoadIssue | null) => void;
  filterType: string;
  setFilterType: (type: string) => void;
  filterSeverity: string;
  setFilterSeverity: (severity: string) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}) => (
  <div className="flex flex-col h-full bg-gradient-to-b from-white to-gray-50">
    {/* Advanced Header - Mobile Responsive */}
    <div className="p-4 md:p-6 bg-gradient-to-r from-blue-50 to-orange-50 border-b border-gray-200/50">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-blue-500 to-orange-500 rounded-2xl flex items-center justify-center">
          <BarChart3 className="w-4 h-4 md:w-5 md:h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg md:text-xl font-bold text-gray-900">Dashboard</h3>
          <p className="text-xs md:text-sm text-gray-600">Real-time road intelligence</p>
        </div>
      </div>

      {/* Advanced Search - Mobile Responsive */}
      <div className="relative mb-4">
        <Search className="absolute left-3 md:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 md:w-5 md:h-5" />
        <Input
          placeholder="Search locations, issues..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 md:pl-12 h-10 md:h-12 rounded-2xl border-gray-200 bg-white/80 backdrop-blur-sm focus:bg-white transition-all duration-300 text-sm md:text-base"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchQuery('')}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 hover:bg-gray-100 rounded-xl"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Advanced Filters - Mobile Responsive */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="h-10 md:h-11 rounded-xl border-gray-200 bg-white/80 backdrop-blur-sm text-sm md:text-base">
            <SelectValue placeholder="Issue Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">🔍 All Types</SelectItem>
            <SelectItem value="pothole">🕳️ Potholes</SelectItem>
            <SelectItem value="construction">🚧 Construction</SelectItem>
            <SelectItem value="accident">🚗 Accidents</SelectItem>
            <SelectItem value="police">👮 Police</SelectItem>
            <SelectItem value="camera">📷 Cameras</SelectItem>
            <SelectItem value="sos">🆘 SOS</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterSeverity} onValueChange={setFilterSeverity}>
          <SelectTrigger className="h-10 md:h-11 rounded-xl border-gray-200 bg-white/80 backdrop-blur-sm text-sm md:text-base">
            <SelectValue placeholder="Severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">📊 All Levels</SelectItem>
            <SelectItem value="low">🟢 Low</SelectItem>
            <SelectItem value="medium">🟡 Medium</SelectItem>
            <SelectItem value="high">🟠 High</SelectItem>
            <SelectItem value="critical">🔴 Critical</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Filter Status - Mobile Responsive */}
      <div className="flex items-center justify-between mt-4 flex-wrap gap-2">
        <div className="flex items-center gap-2 flex-wrap">
          <Badge variant="outline" className="bg-white/80 text-xs">
            {issues.length} results
          </Badge>
          {(filterType !== 'all' || filterSeverity !== 'all' || searchQuery) && (
            <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs">
              Filtered
            </Badge>
          )}
        </div>
        {(filterType !== 'all' || filterSeverity !== 'all' || searchQuery) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setFilterType('all');
              setFilterSeverity('all');
              setSearchQuery('');
            }}
            className="text-xs hover:bg-white/50 flex-shrink-0"
          >
            Clear filters
          </Button>
        )}
      </div>
    </div>

    <div className="flex-1 overflow-y-auto">
      {selectedIssue ? (
        <div className="p-6">
          {/* Advanced Issue Details Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onIssueSelect(null)}
                className="hover:bg-gray-100 rounded-xl"
              >
                <ChevronRight className="w-4 h-4 rotate-180" />
              </Button>
              <h4 className="text-lg font-bold text-gray-900">Issue Details</h4>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="hover:bg-blue-50 text-blue-600">
                <Share2 className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="hover:bg-purple-50 text-purple-600">
                <Bookmark className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Advanced Issue Card */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                    <AlertTriangle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl capitalize text-gray-900">{selectedIssue.type}</CardTitle>
                    <p className="text-sm text-gray-500 mt-1">{selectedIssue.location}</p>
                  </div>
                </div>
                <Badge
                  variant={selectedIssue.severity === 'high' ? 'destructive' : 'default'}
                  className="text-sm px-3 py-1"
                >
                  {selectedIssue.severity.toUpperCase()}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-xl">
                  <h5 className="font-semibold text-blue-900 mb-2">Status</h5>
                  <Badge variant="outline" className="bg-white border-blue-200 text-blue-700">
                    {selectedIssue.status || 'Active'}
                  </Badge>
                </div>
                <div className="bg-green-50 p-4 rounded-xl">
                  <h5 className="font-semibold text-green-900 mb-2">Verified</h5>
                  <div className="flex items-center gap-2">
                    {selectedIssue.verified ? (
                      <Badge className="bg-green-600 text-white">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Verified
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="border-orange-300 text-orange-700">
                        Pending
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-xl">
                <h5 className="font-semibold text-gray-900 mb-2">Description</h5>
                <p className="text-gray-700 leading-relaxed">{selectedIssue.description}</p>
              </div>

              <div className="bg-purple-50 p-4 rounded-xl">
                <h5 className="font-semibold text-purple-900 mb-3">Reporter Information</h5>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{selectedIssue.reportedBy}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs text-gray-600">4.8</span>
                      </div>
                      <div className="w-1 h-1 bg-gray-300 rounded-full" />
                      <span className="text-xs text-gray-600">Verified Reporter</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
                  <Navigation className="w-4 h-4 mr-2" />
                  Navigate Here
                </Button>
                <Button variant="outline" className="flex-1 hover:bg-red-50 hover:border-red-200 hover:text-red-600">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Report Issue
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h4 className="text-lg font-bold text-gray-900">Recent Issues</h4>
            <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
              {issues.length} total
            </Badge>
          </div>

          <div className="space-y-3">
            {issues.slice(0, 20).map((issue, index) => (
              <Card
                key={issue.id}
                className="cursor-pointer hover:shadow-md transition-all duration-300 border-0 bg-gradient-to-r from-white to-gray-50 hover:from-blue-50 hover:to-purple-50"
                onClick={() => onIssueSelect(issue)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold capitalize text-gray-900">{issue.type}</span>
                        <Badge
                          variant={issue.severity === 'high' ? 'destructive' : 'secondary'}
                          className="text-xs"
                        >
                          {issue.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{issue.location}</p>
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <User className="w-3 h-3" />
                        <span>{issue.reportedBy}</span>
                        <div className="w-1 h-1 bg-gray-300 rounded-full" />
                        <Clock className="w-3 h-3" />
                        <span>2h ago</span>
                      </div>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
            ))}

            {issues.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <h5 className="font-semibold text-gray-900 mb-2">No issues found</h5>
                <p className="text-gray-600 text-sm">Try adjusting your filters or search terms</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  </div>
);

// Advanced Navigation Panel Component
const NavigationPanel = ({
  routeStart,
  setRouteStart,
  routeEnd,
  setRouteEnd,
  isNavigating,
  onStartNavigation,
  currentLocation,
  locationPermission,
  searchingLocation,
  startSuggestions,
  endSuggestions,
  showStartSuggestions,
  showEndSuggestions,
  onStartLocationSearch,
  onEndLocationSearch,
  onSelectStartLocation,
  onSelectEndLocation,
  onUseCurrentLocation,
  routeData,
  navigationStep,
  calculatingRoute,
  navigationError
}: {
  routeStart: string;
  setRouteStart: (start: string) => void;
  routeEnd: string;
  setRouteEnd: (end: string) => void;
  isNavigating: boolean;
  onStartNavigation: () => void;
  currentLocation: {lat: number, lng: number} | null;
  locationPermission: 'granted' | 'denied' | 'prompt';
  searchingLocation: boolean;
  startSuggestions: any[];
  endSuggestions: any[];
  showStartSuggestions: boolean;
  showEndSuggestions: boolean;
  onStartLocationSearch: (query: string) => void;
  onEndLocationSearch: (query: string) => void;
  onSelectStartLocation: (location: any) => void;
  onSelectEndLocation: (location: any) => void;
  onUseCurrentLocation: () => void;
  routeData: any;
  navigationStep: number;
  calculatingRoute: boolean;
  navigationError: string | null;
}) => (
  <div className="flex flex-col h-full bg-gradient-to-b from-white to-gray-50">
    {/* Advanced Navigation Header */}
    <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 border-b border-gray-200/50">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center">
          <Navigation className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900">Navigation</h3>
          <p className="text-sm text-gray-600">Smart route planning</p>
        </div>
      </div>

      {/* Advanced Route Input with Search */}
      <div className="space-y-4">
        {/* From Location */}
        <div className="relative">
          <label className="text-sm font-semibold text-gray-700 mb-2 block">From</label>
          <div className="relative">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-green-500 rounded-full"></div>
            <Input
              placeholder="Current location or enter address"
              value={routeStart}
              onChange={(e) => onStartLocationSearch(e.target.value)}
              className="pl-12 h-12 rounded-2xl border-gray-200 bg-white/80 backdrop-blur-sm focus:bg-white transition-all duration-300"
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 hover:bg-green-100 text-green-600 rounded-xl"
              onClick={onUseCurrentLocation}
              disabled={searchingLocation}
            >
              {searchingLocation ? (
                <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
              ) : (
                <Target className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Start Location Suggestions */}
          {showStartSuggestions && startSuggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto">
              {startSuggestions.map((suggestion, index) => (
                <div
                  key={suggestion.id || index}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => onSelectStartLocation(suggestion)}
                >
                  <div className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{suggestion.address}</p>
                      <p className="text-xs text-gray-500">{suggestion.name}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* To Location */}
        <div className="relative">
          <label className="text-sm font-semibold text-gray-700 mb-2 block">To</label>
          <div className="relative">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-red-500 rounded-full"></div>
            <Input
              placeholder="Enter destination address"
              value={routeEnd}
              onChange={(e) => onEndLocationSearch(e.target.value)}
              className="pl-12 h-12 rounded-2xl border-gray-200 bg-white/80 backdrop-blur-sm focus:bg-white transition-all duration-300"
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 hover:bg-blue-100 text-blue-600 rounded-xl"
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>

          {/* End Location Suggestions */}
          {showEndSuggestions && endSuggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto">
              {endSuggestions.map((suggestion, index) => (
                <div
                  key={suggestion.id || index}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => onSelectEndLocation(suggestion)}
                >
                  <div className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{suggestion.address}</p>
                      <p className="text-xs text-gray-500">{suggestion.name}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Navigation Error */}
        {navigationError && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700">{navigationError}</p>
            </div>
          </div>
        )}

        {/* Location Permission Status */}
        {locationPermission === 'denied' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <p className="text-sm text-yellow-700">
                Location access denied. Enable location services for current location.
              </p>
            </div>
          </div>
        )}

        {currentLocation && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-3">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-green-600" />
              <p className="text-sm text-green-700">
                Current location: {currentLocation.lat.toFixed(4)}, {currentLocation.lng.toFixed(4)}
              </p>
            </div>
          </div>
        )}

        {/* Route Options */}
        <div className="grid grid-cols-3 gap-2 mt-4">
          <Button variant="outline" size="sm" className="rounded-xl bg-white/80">
            <Zap className="w-3 h-3 mr-1" />
            Fastest
          </Button>
          <Button variant="outline" size="sm" className="rounded-xl bg-white/80">
            <Route className="w-3 h-3 mr-1" />
            Shortest
          </Button>
          <Button variant="outline" size="sm" className="rounded-xl bg-white/80">
            <Shield className="w-3 h-3 mr-1" />
            Safest
          </Button>
        </div>

        <Button
          className="w-full h-12 rounded-2xl bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold shadow-lg disabled:opacity-50"
          onClick={onStartNavigation}
          disabled={!routeStart || !routeEnd || calculatingRoute}
        >
          {calculatingRoute ? (
            <>
              <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Calculating Route...
            </>
          ) : (
            <>
              <Navigation className="w-5 h-5 mr-2" />
              Start Navigation
            </>
          )}
        </Button>
      </div>
    </div>

    <div className="flex-1 p-6">
      {isNavigating && routeData ? (
        <div className="space-y-6">
          {/* Active Route Card with Real Data */}
          <Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-blue-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center">
                    <Navigation className="w-5 h-5 text-white animate-pulse" />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900">Active Route</h4>
                    <p className="text-sm text-gray-600">Real-time navigation</p>
                  </div>
                </div>
                <Badge className="bg-green-600 text-white">
                  <Activity className="w-3 h-3 mr-1" />
                  LIVE
                </Badge>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">{routeStart}</span>
                </div>
                <div className="w-px h-6 bg-gray-300 ml-1.5 my-2"></div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">{routeEnd}</span>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">
                    {(routeData.summary.distance / 1000).toFixed(1)} km
                  </div>
                  <div className="text-xs text-gray-600">Distance</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {Math.round(routeData.summary.duration / 60)} min
                  </div>
                  <div className="text-xs text-gray-600">ETA</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-orange-600">
                    {routeData.fallback ? 'EST' : (routeData.segments?.length || 0)}
                  </div>
                  <div className="text-xs text-gray-600">
                    {routeData.fallback ? 'Estimated' : 'Segments'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fallback Route Notice */}
          {routeData.fallback && (
            <div className="bg-amber-50 border border-amber-200 rounded-xl p-3 mb-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-amber-600" />
                <p className="text-sm text-amber-700">
                  {routeData.fallbackMessage || 'Using estimated route - GPS navigation recommended'}
                </p>
              </div>
            </div>
          )}

          {/* Real Route Instructions */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Compass className="w-5 h-5 text-blue-600" />
                Turn-by-Turn Directions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {routeData.segments?.[0]?.steps?.map((step: any, index: number) => (
                  <div
                    key={index}
                    className={`flex items-center gap-4 p-3 rounded-xl ${
                      index === navigationStep ? 'bg-blue-50 border-2 border-blue-200' : 'bg-gray-50'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                      index === navigationStep ? 'bg-blue-600' : 'bg-gray-400'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{step.instruction}</p>
                      <p className="text-sm text-gray-600">
                        Continue for {(step.distance / 1000).toFixed(1)}km
                      </p>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-bold ${
                        index === navigationStep ? 'text-blue-600' : 'text-gray-600'
                      }`}>
                        {(step.distance / 1000).toFixed(1)}km
                      </div>
                      <div className="text-xs text-gray-500">
                        {Math.round(step.duration / 60)} min
                      </div>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-4 text-gray-500">
                    <Compass className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>Loading route instructions...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Navigation Controls */}
          <div className="space-y-3">
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1 hover:bg-blue-50 border-blue-200"
                onClick={() => {
                  if (navigationStep > 0) {
                    setNavigationStep(navigationStep - 1);
                  }
                }}
                disabled={navigationStep === 0}
              >
                <ChevronRight className="w-4 h-4 mr-2 rotate-180" />
                Previous
              </Button>
              <Button
                variant="outline"
                className="flex-1 hover:bg-green-50 border-green-200"
                onClick={() => {
                  const maxSteps = routeData.segments?.[0]?.steps?.length || 0;
                  if (navigationStep < maxSteps - 1) {
                    setNavigationStep(navigationStep + 1);
                  }
                }}
                disabled={navigationStep >= (routeData.segments?.[0]?.steps?.length || 0) - 1}
              >
                Next
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>

            <div className="flex gap-3">
              <Button variant="outline" className="flex-1 hover:bg-orange-50 border-orange-200">
                <MessageSquare className="w-4 h-4 mr-2" />
                Report Issue
              </Button>
              <Button
                variant="destructive"
                className="flex-1"
                onClick={() => {
                  setIsNavigating(false);
                  setRouteData(null);
                  setNavigationStep(0);
                  console.log('🛑 Navigation ended');
                }}
              >
                <X className="w-4 h-4 mr-2" />
                End Navigation
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="w-20 h-20 bg-gradient-to-br from-green-100 to-blue-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
            <Navigation className="w-10 h-10 text-gray-400" />
          </div>
          <h5 className="text-lg font-semibold text-gray-900 mb-2">Ready to Navigate</h5>
          <p className="text-gray-600 mb-6">Enter your start and destination to begin smart navigation</p>

          {/* Quick Destinations */}
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700 mb-3">Quick Destinations</p>
            <div className="grid grid-cols-1 gap-2">
              <Button
                variant="outline"
                className="justify-start hover:bg-blue-50 border-blue-200"
                onClick={() => {
                  onUseCurrentLocation();
                  onEndLocationSearch("Harare CBD");
                }}
              >
                <MapPin className="w-4 h-4 mr-2" />
                Harare CBD
              </Button>
              <Button
                variant="outline"
                className="justify-start hover:bg-green-50 border-green-200"
                onClick={() => {
                  onUseCurrentLocation();
                  onEndLocationSearch("Airport");
                }}
              >
                <Car className="w-4 h-4 mr-2" />
                Airport
              </Button>
              <Button
                variant="outline"
                className="justify-start hover:bg-orange-50 border-orange-200"
                onClick={() => {
                  onUseCurrentLocation();
                  onEndLocationSearch("University of Zimbabwe");
                }}
              >
                <Star className="w-4 h-4 mr-2" />
                University
              </Button>
              <Button
                variant="outline"
                className="justify-start hover:bg-purple-50 border-purple-200"
                onClick={() => {
                  onUseCurrentLocation();
                  onEndLocationSearch("Eastgate");
                }}
              >
                <MapPin className="w-4 h-4 mr-2" />
                Eastgate Mall
              </Button>
              <Button
                variant="outline"
                className="justify-start hover:bg-pink-50 border-pink-200"
                onClick={() => {
                  onUseCurrentLocation();
                  onEndLocationSearch("Avondale");
                }}
              >
                <MapPin className="w-4 h-4 mr-2" />
                Avondale
              </Button>
              <Button
                variant="outline"
                className="justify-start hover:bg-indigo-50 border-indigo-200"
                onClick={() => {
                  onUseCurrentLocation();
                  onEndLocationSearch("Sam Levy Village");
                }}
              >
                <MapPin className="w-4 h-4 mr-2" />
                Sam Levy Village
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  </div>
);

// Report Panel Component
const ReportPanel = () => (
  <div className="flex flex-col h-full">
    <div className="p-4 border-b border-gray-200">
      <h3 className="font-semibold text-gray-900 mb-3">📝 Report Issue</h3>
    </div>

    <div className="flex-1 p-4">
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium text-gray-700">Issue Type</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select issue type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pothole">Pothole</SelectItem>
              <SelectItem value="construction">Construction</SelectItem>
              <SelectItem value="accident">Accident</SelectItem>
              <SelectItem value="police">Police Checkpoint</SelectItem>
              <SelectItem value="camera">Speed Camera</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Location</label>
          <Input placeholder="Enter location or use current location" />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Description</label>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={4}
            placeholder="Describe the issue..."
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Severity</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button className="w-full">
          <Plus className="w-4 h-4 mr-2" />
          Submit Report
        </Button>
      </div>
    </div>
  </div>
);

// SOS Panel Component
const SOSPanel = ({ onSOSRequest }: { onSOSRequest: () => void }) => (
  <div className="flex flex-col h-full">
    <div className="p-4 border-b border-red-200 bg-red-50">
      <h3 className="font-semibold text-red-900 mb-3">🆘 Emergency SOS</h3>
      <p className="text-red-700 text-sm">Emergency assistance and safety features</p>
    </div>

    <div className="flex-1 p-4">
      <div className="space-y-4">
        <Card className="border-red-200">
          <CardContent className="p-4">
            <h4 className="font-semibold text-red-900 mb-2">Emergency Contacts</h4>
            <div className="space-y-2">
              <Button variant="destructive" className="w-full justify-start">
                <Phone className="w-4 h-4 mr-2" />
                Police: 999
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                <Phone className="w-4 h-4 mr-2" />
                Ambulance: 994
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                <Phone className="w-4 h-4 mr-2" />
                Fire: 993
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Quick SOS</h4>
            <p className="text-sm text-gray-600 mb-3">
              Send your location to emergency contacts
            </p>
            <Button variant="destructive" className="w-full" size="lg">
              <Shield className="w-5 h-5 mr-2" />
              SEND SOS ALERT
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Safety Features</h4>
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <MapPin className="w-4 h-4 mr-2" />
                Share Live Location
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Car className="w-4 h-4 mr-2" />
                Request Roadside Assistance
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Star className="w-4 h-4 mr-2" />
                Find Safe Places
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
);

// Analytics Panel Component
const AnalyticsPanel = ({ issues }: { issues: RoadIssue[] }) => {
  const stats = {
    total: issues.length,
    byType: issues.reduce((acc, issue) => {
      acc[issue.type] = (acc[issue.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    bySeverity: issues.reduce((acc, issue) => {
      acc[issue.severity] = (acc[issue.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900 mb-3">📊 Analytics</h3>
      </div>

      <div className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                  <div className="text-sm text-gray-600">Total Issues</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {stats.bySeverity.high || 0}
                  </div>
                  <div className="text-sm text-gray-600">High Priority</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">By Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="capitalize">{type}</span>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">By Severity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.bySeverity).map(([severity, count]) => (
                  <div key={severity} className="flex justify-between items-center">
                    <span className="capitalize">{severity}</span>
                    <Badge
                      variant={severity === 'high' ? 'destructive' : 'secondary'}
                    >
                      {count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Advanced Navigation Button Component
const NavigationButton = ({
  icon: Icon,
  label,
  isActive,
  onClick,
  sidebarOpen,
  badge,
  gradient,
  isEmergency = false
}: {
  icon: any;
  label: string;
  isActive: boolean;
  onClick: () => void;
  sidebarOpen: boolean;
  badge?: string | number;
  gradient: string;
  isEmergency?: boolean;
}) => (
  <Button
    variant="ghost"
    onClick={onClick}
    className={`
      w-full justify-start h-12 rounded-xl transition-all duration-300 relative overflow-hidden group
      ${isActive
        ? `bg-gradient-to-r ${gradient} text-white shadow-lg transform scale-105`
        : `hover:bg-gradient-to-r ${gradient} hover:text-white hover:shadow-md hover:scale-102`
      }
      ${isEmergency ? 'animate-pulse' : ''}
    `}
  >
    <div className="flex items-center gap-3 relative z-10">
      <Icon className={`w-5 h-5 ${isActive ? 'text-white' : ''}`} />
      {sidebarOpen && (
        <>
          <span className="font-medium">{label}</span>
          {badge && (
            <Badge
              variant={isActive ? "secondary" : "outline"}
              className={`ml-auto text-xs ${
                isActive
                  ? 'bg-white/20 text-white border-white/30'
                  : 'bg-gray-100 text-gray-600'
              }`}
            >
              {badge}
            </Badge>
          )}
        </>
      )}
    </div>
    {!isActive && (
      <div className={`absolute inset-0 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
    )}
  </Button>
);

// Advanced Stat Card Component - Mobile Responsive
const StatCard = ({
  icon: Icon,
  label,
  value,
  color
}: {
  icon: any;
  label: string;
  value: number;
  color: string;
}) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 text-blue-600',
    green: 'from-green-500 to-green-600 text-green-600',
    red: 'from-red-500 to-red-600 text-red-600',
    orange: 'from-orange-500 to-orange-600 text-orange-600',
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-2 md:p-3 border border-gray-200/50 hover:shadow-md transition-all duration-300">
      <div className="flex items-center gap-1 md:gap-2 mb-1">
        <div className={`w-5 h-5 md:w-6 md:h-6 bg-gradient-to-br ${colorClasses[color as keyof typeof colorClasses]} rounded-lg flex items-center justify-center`}>
          <Icon className="w-2.5 h-2.5 md:w-3 md:h-3 text-white" />
        </div>
        <span className="text-xs font-medium text-gray-600 truncate">{label}</span>
      </div>
      <div className={`text-base md:text-lg font-bold ${colorClasses[color as keyof typeof colorClasses].split(' ')[2]}`}>
        {value}
      </div>
    </div>
  );
};

export default MainApp;
