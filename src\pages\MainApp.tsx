/**
 * MainApp - Unified Navigation Application
 * All-in-one interface combining dashboard, map, navigation, and SOS features
 */

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import InteractiveMap, { RoadIssue } from "@/components/InteractiveMap";
import { dataService } from "@/services/dataService";
import { useAuth } from "@/contexts/AuthContext";
import {
  MapPin,
  Navigation,
  AlertTriangle,
  Settings,
  User,
  Menu,
  X,
  Route,
  Shield,
  BarChart3,
  Plus,
  Search,
  Filter,
  Layers,
  Phone,
  Car,
  Clock,
  Star
} from "lucide-react";

type AppMode = 'dashboard' | 'navigation' | 'report' | 'sos' | 'analytics';
type MapTheme = 'standard' | 'satellite' | 'dark' | 'terrain';

const MainApp = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  
  // Core app state
  const [currentMode, setCurrentMode] = useState<AppMode>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mapTheme, setMapTheme] = useState<MapTheme>('standard');
  
  // Data state
  const [allIssues, setAllIssues] = useState<RoadIssue[]>([]);
  const [filteredIssues, setFilteredIssues] = useState<RoadIssue[]>([]);
  const [selectedIssue, setSelectedIssue] = useState<RoadIssue | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter state
  const [filterType, setFilterType] = useState("all");
  const [filterSeverity, setFilterSeverity] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [showLayers, setShowLayers] = useState(false);
  const [visibleLayers, setVisibleLayers] = useState({
    potholes: true,
    construction: true,
    accidents: true,
    police: true,
    cameras: true,
    sos: true,
    traffic: false,
  });

  // Navigation state
  const [routeStart, setRouteStart] = useState("");
  const [routeEnd, setRouteEnd] = useState("");
  const [isNavigating, setIsNavigating] = useState(false);

  // Load data on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🚀 Loading RoadPulse data...');
        setError(null);
        
        await dataService.initialize();
        const data = await dataService.getIssues();
        
        console.log(`✅ Loaded ${data.length} issues from Supabase`);
        setAllIssues(data);
        setFilteredIssues(data);
      } catch (error) {
        console.error('❌ Failed to load data:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to load data';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Filter issues when filters change
  useEffect(() => {
    let filtered = [...allIssues];

    // Filter by layer visibility
    filtered = filtered.filter(issue => {
      switch (issue.type) {
        case 'pothole': return visibleLayers.potholes;
        case 'construction': return visibleLayers.construction;
        case 'accident': return visibleLayers.accidents;
        case 'police': return visibleLayers.police;
        case 'camera': return visibleLayers.cameras;
        case 'sos': return visibleLayers.sos;
        default: return true;
      }
    });

    // Filter by type
    if (filterType !== "all") {
      filtered = filtered.filter(issue => issue.type === filterType);
    }

    // Filter by severity
    if (filterSeverity !== "all") {
      filtered = filtered.filter(issue => issue.severity === filterSeverity);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(issue => 
        issue.location.toLowerCase().includes(query) ||
        issue.description.toLowerCase().includes(query) ||
        issue.reportedBy.toLowerCase().includes(query)
      );
    }

    setFilteredIssues(filtered);
  }, [allIssues, filterType, filterSeverity, searchQuery, visibleLayers]);

  // Event handlers
  const handleModeChange = (mode: AppMode) => {
    console.log('🔄 Switching to mode:', mode);
    setCurrentMode(mode);
    setSelectedIssue(null);
  };

  const handleIssueClick = (issue: RoadIssue) => {
    setSelectedIssue(issue);
    setCurrentMode('dashboard'); // Switch to dashboard to show details
  };

  const handleLayerToggle = (layerType: keyof typeof visibleLayers) => {
    setVisibleLayers(prev => ({
      ...prev,
      [layerType]: !prev[layerType]
    }));
  };

  const handleStartNavigation = () => {
    if (routeStart && routeEnd) {
      setIsNavigating(true);
      setCurrentMode('navigation');
      console.log(`🧭 Starting navigation from ${routeStart} to ${routeEnd}`);
    }
  };

  const handleSOSRequest = () => {
    setCurrentMode('sos');
    console.log('🆘 SOS mode activated');
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Redirect if not authenticated
  if (!isAuthenticated) {
    navigate('/');
    return null;
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-80' : 'w-16'} transition-all duration-300 bg-white border-r border-gray-200 flex flex-col`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="font-bold text-gray-900">RoadPulse</h1>
                  <p className="text-xs text-gray-500">Navigation System</p>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        {/* Navigation Menu */}
        <div className="flex-1 p-4">
          <div className="space-y-2">
            <Button
              variant={currentMode === 'dashboard' ? 'default' : 'ghost'}
              className="w-full justify-start"
              onClick={() => handleModeChange('dashboard')}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              {sidebarOpen && 'Dashboard'}
            </Button>
            
            <Button
              variant={currentMode === 'navigation' ? 'default' : 'ghost'}
              className="w-full justify-start"
              onClick={() => handleModeChange('navigation')}
            >
              <Navigation className="w-4 h-4 mr-2" />
              {sidebarOpen && 'Navigation'}
            </Button>
            
            <Button
              variant={currentMode === 'report' ? 'default' : 'ghost'}
              className="w-full justify-start"
              onClick={() => handleModeChange('report')}
            >
              <Plus className="w-4 h-4 mr-2" />
              {sidebarOpen && 'Report Issue'}
            </Button>
            
            <Button
              variant={currentMode === 'sos' ? 'default' : 'ghost'}
              className="w-full justify-start bg-red-50 hover:bg-red-100 text-red-700"
              onClick={() => handleModeChange('sos')}
            >
              <Shield className="w-4 h-4 mr-2" />
              {sidebarOpen && 'SOS Emergency'}
            </Button>
            
            <Button
              variant={currentMode === 'analytics' ? 'default' : 'ghost'}
              className="w-full justify-start"
              onClick={() => handleModeChange('analytics')}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              {sidebarOpen && 'Analytics'}
            </Button>
          </div>

          {sidebarOpen && (
            <>
              {/* Quick Stats */}
              <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Live Status</h3>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span>Total Issues:</span>
                    <Badge variant="secondary">{allIssues.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Visible:</span>
                    <Badge variant="outline">{filteredIssues.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>SOS Active:</span>
                    <Badge variant="destructive">{allIssues.filter(i => i.type === 'sos').length}</Badge>
                  </div>
                </div>
              </div>

              {/* User Profile */}
              <div className="mt-6 p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <User className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium">{user?.email || 'User'}</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={handleLogout}
                >
                  Logout
                </Button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-semibold text-gray-900 capitalize">
              {currentMode === 'sos' ? '🆘 Emergency Mode' : currentMode}
            </h2>
            <Badge variant="outline">
              {mapTheme === 'standard' && '🗺️ Standard'}
              {mapTheme === 'satellite' && '🛰️ Satellite'}
              {mapTheme === 'dark' && '🌙 Dark'}
              {mapTheme === 'terrain' && '🏔️ Terrain'}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowLayers(!showLayers)}
              className={showLayers ? "bg-gray-100" : ""}
            >
              <Layers className="w-4 h-4 mr-1" />
              Layers
            </Button>
            
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Layers Panel */}
        {showLayers && (
          <div className="bg-white border-b border-gray-200 p-4 shadow-sm">
            <div className="max-w-6xl mx-auto">
              <h3 className="font-semibold text-gray-900 mb-3">Map Layers & Themes</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Layer toggles */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Incidents</h4>
                  <div className="space-y-1">
                    {Object.entries(visibleLayers).map(([key, value]) => (
                      <label key={key} className="flex items-center text-sm cursor-pointer">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={() => handleLayerToggle(key as keyof typeof visibleLayers)}
                          className="mr-2"
                        />
                        <span className="capitalize">{key} ({allIssues.filter(i => i.type === key).length})</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Map themes */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Map Style</h4>
                  <div className="space-y-1">
                    {(['standard', 'satellite', 'dark', 'terrain'] as MapTheme[]).map((theme) => (
                      <label key={theme} className="flex items-center text-sm cursor-pointer">
                        <input
                          type="radio"
                          name="mapTheme"
                          checked={mapTheme === theme}
                          onChange={() => setMapTheme(theme)}
                          className="mr-2"
                        />
                        <span className="capitalize">{theme}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 flex">
          {/* Map */}
          <div className="flex-1">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600">Loading RoadPulse...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full bg-red-50">
                <div className="text-center p-8">
                  <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
                  <h3 className="text-lg font-semibold text-red-700 mb-2">Error</h3>
                  <p className="text-red-600 mb-4">{error}</p>
                  <Button onClick={() => window.location.reload()}>Retry</Button>
                </div>
              </div>
            ) : (
              <InteractiveMap
                height="100%"
                center={[-17.8292, 31.0522]}
                zoom={13}
                issues={filteredIssues}
                showControls={true}
                onIssueClick={handleIssueClick}
                mapTheme={mapTheme}
              />
            )}
          </div>

          {/* Right Sidebar - Mode-specific content */}
          <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
            {currentMode === 'dashboard' && (
              <DashboardPanel
                issues={filteredIssues}
                selectedIssue={selectedIssue}
                onIssueSelect={setSelectedIssue}
                filterType={filterType}
                setFilterType={setFilterType}
                filterSeverity={filterSeverity}
                setFilterSeverity={setFilterSeverity}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
              />
            )}

            {currentMode === 'navigation' && (
              <NavigationPanel
                routeStart={routeStart}
                setRouteStart={setRouteStart}
                routeEnd={routeEnd}
                setRouteEnd={setRouteEnd}
                isNavigating={isNavigating}
                onStartNavigation={handleStartNavigation}
              />
            )}

            {currentMode === 'report' && (
              <ReportPanel />
            )}

            {currentMode === 'sos' && (
              <SOSPanel onSOSRequest={handleSOSRequest} />
            )}

            {currentMode === 'analytics' && (
              <AnalyticsPanel issues={allIssues} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Dashboard Panel Component
const DashboardPanel = ({
  issues,
  selectedIssue,
  onIssueSelect,
  filterType,
  setFilterType,
  filterSeverity,
  setFilterSeverity,
  searchQuery,
  setSearchQuery
}: {
  issues: RoadIssue[];
  selectedIssue: RoadIssue | null;
  onIssueSelect: (issue: RoadIssue | null) => void;
  filterType: string;
  setFilterType: (type: string) => void;
  filterSeverity: string;
  setFilterSeverity: (severity: string) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}) => (
  <div className="flex flex-col h-full">
    <div className="p-4 border-b border-gray-200">
      <h3 className="font-semibold text-gray-900 mb-3">Dashboard</h3>

      {/* Search */}
      <div className="relative mb-3">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search issues..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Filters */}
      <div className="grid grid-cols-2 gap-2">
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger>
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="pothole">Potholes</SelectItem>
            <SelectItem value="construction">Construction</SelectItem>
            <SelectItem value="accident">Accidents</SelectItem>
            <SelectItem value="police">Police</SelectItem>
            <SelectItem value="camera">Cameras</SelectItem>
            <SelectItem value="sos">SOS</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterSeverity} onValueChange={setFilterSeverity}>
          <SelectTrigger>
            <SelectValue placeholder="Severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div className="flex-1 overflow-y-auto">
      {selectedIssue ? (
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold">Issue Details</h4>
            <Button variant="ghost" size="sm" onClick={() => onIssueSelect(null)}>
              <X className="w-4 h-4" />
            </Button>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="capitalize">{selectedIssue.type}</CardTitle>
                <Badge variant={selectedIssue.severity === 'high' ? 'destructive' : 'default'}>
                  {selectedIssue.severity}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium">Location</h5>
                  <p className="text-gray-600">{selectedIssue.location}</p>
                </div>
                <div>
                  <h5 className="font-medium">Description</h5>
                  <p className="text-gray-600">{selectedIssue.description}</p>
                </div>
                <div>
                  <h5 className="font-medium">Reported By</h5>
                  <p className="text-gray-600">{selectedIssue.reportedBy}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="p-4">
          <h4 className="font-semibold mb-3">Recent Issues ({issues.length})</h4>
          <div className="space-y-2">
            {issues.slice(0, 20).map((issue) => (
              <Card
                key={issue.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => onIssueSelect(issue)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium capitalize text-sm">{issue.type}</span>
                    <Badge variant={issue.severity === 'high' ? 'destructive' : 'secondary'} className="text-xs">
                      {issue.severity}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 truncate">{issue.location}</p>
                  <p className="text-xs text-gray-500">By {issue.reportedBy}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  </div>
);

// Navigation Panel Component
const NavigationPanel = ({
  routeStart,
  setRouteStart,
  routeEnd,
  setRouteEnd,
  isNavigating,
  onStartNavigation
}: {
  routeStart: string;
  setRouteStart: (start: string) => void;
  routeEnd: string;
  setRouteEnd: (end: string) => void;
  isNavigating: boolean;
  onStartNavigation: () => void;
}) => (
  <div className="flex flex-col h-full">
    <div className="p-4 border-b border-gray-200">
      <h3 className="font-semibold text-gray-900 mb-3">🧭 Navigation</h3>

      <div className="space-y-3">
        <div>
          <label className="text-sm font-medium text-gray-700">From</label>
          <Input
            placeholder="Enter starting location"
            value={routeStart}
            onChange={(e) => setRouteStart(e.target.value)}
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">To</label>
          <Input
            placeholder="Enter destination"
            value={routeEnd}
            onChange={(e) => setRouteEnd(e.target.value)}
          />
        </div>

        <Button
          className="w-full"
          onClick={onStartNavigation}
          disabled={!routeStart || !routeEnd}
        >
          <Navigation className="w-4 h-4 mr-2" />
          Start Navigation
        </Button>
      </div>
    </div>

    <div className="flex-1 p-4">
      {isNavigating ? (
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900">Active Route</h4>
            <p className="text-blue-700 text-sm">{routeStart} → {routeEnd}</p>
          </div>

          <div className="space-y-2">
            <h5 className="font-medium">Route Instructions</h5>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs">1</div>
                <span>Head north on current road</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs">2</div>
                <span>Turn right at next intersection</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs">3</div>
                <span>Continue for 2.5 km</span>
              </div>
            </div>
          </div>

          <Button variant="destructive" className="w-full">
            End Navigation
          </Button>
        </div>
      ) : (
        <div className="text-center text-gray-500">
          <Navigation className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>Enter start and destination to begin navigation</p>
        </div>
      )}
    </div>
  </div>
);

// Report Panel Component
const ReportPanel = () => (
  <div className="flex flex-col h-full">
    <div className="p-4 border-b border-gray-200">
      <h3 className="font-semibold text-gray-900 mb-3">📝 Report Issue</h3>
    </div>

    <div className="flex-1 p-4">
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium text-gray-700">Issue Type</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select issue type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pothole">Pothole</SelectItem>
              <SelectItem value="construction">Construction</SelectItem>
              <SelectItem value="accident">Accident</SelectItem>
              <SelectItem value="police">Police Checkpoint</SelectItem>
              <SelectItem value="camera">Speed Camera</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Location</label>
          <Input placeholder="Enter location or use current location" />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Description</label>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={4}
            placeholder="Describe the issue..."
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Severity</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button className="w-full">
          <Plus className="w-4 h-4 mr-2" />
          Submit Report
        </Button>
      </div>
    </div>
  </div>
);

// SOS Panel Component
const SOSPanel = ({ onSOSRequest }: { onSOSRequest: () => void }) => (
  <div className="flex flex-col h-full">
    <div className="p-4 border-b border-red-200 bg-red-50">
      <h3 className="font-semibold text-red-900 mb-3">🆘 Emergency SOS</h3>
      <p className="text-red-700 text-sm">Emergency assistance and safety features</p>
    </div>

    <div className="flex-1 p-4">
      <div className="space-y-4">
        <Card className="border-red-200">
          <CardContent className="p-4">
            <h4 className="font-semibold text-red-900 mb-2">Emergency Contacts</h4>
            <div className="space-y-2">
              <Button variant="destructive" className="w-full justify-start">
                <Phone className="w-4 h-4 mr-2" />
                Police: 999
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                <Phone className="w-4 h-4 mr-2" />
                Ambulance: 994
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                <Phone className="w-4 h-4 mr-2" />
                Fire: 993
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Quick SOS</h4>
            <p className="text-sm text-gray-600 mb-3">
              Send your location to emergency contacts
            </p>
            <Button variant="destructive" className="w-full" size="lg">
              <Shield className="w-5 h-5 mr-2" />
              SEND SOS ALERT
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">Safety Features</h4>
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <MapPin className="w-4 h-4 mr-2" />
                Share Live Location
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Car className="w-4 h-4 mr-2" />
                Request Roadside Assistance
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Star className="w-4 h-4 mr-2" />
                Find Safe Places
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
);

// Analytics Panel Component
const AnalyticsPanel = ({ issues }: { issues: RoadIssue[] }) => {
  const stats = {
    total: issues.length,
    byType: issues.reduce((acc, issue) => {
      acc[issue.type] = (acc[issue.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    bySeverity: issues.reduce((acc, issue) => {
      acc[issue.severity] = (acc[issue.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900 mb-3">📊 Analytics</h3>
      </div>

      <div className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                  <div className="text-sm text-gray-600">Total Issues</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {stats.bySeverity.high || 0}
                  </div>
                  <div className="text-sm text-gray-600">High Priority</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">By Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="capitalize">{type}</span>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">By Severity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.bySeverity).map(([severity, count]) => (
                  <div key={severity} className="flex justify-between items-center">
                    <span className="capitalize">{severity}</span>
                    <Badge
                      variant={severity === 'high' ? 'destructive' : 'secondary'}
                    >
                      {count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default MainApp;
