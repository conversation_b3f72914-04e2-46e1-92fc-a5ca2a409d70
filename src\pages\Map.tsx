import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import InteractiveMap, { RoadIssue } from "@/components/InteractiveMap";
import { dataService } from "@/services/dataService";
import {
  ArrowLeft,
  MapPin,
  Filter,
  Search,
  Layers,
  Navigation,
  AlertTriangle,
  Construction,
  Droplets,
  Car,
  Settings,
  Route
} from "lucide-react";

const Map = () => {
  const navigate = useNavigate();
  const [selectedIssue, setSelectedIssue] = useState<RoadIssue | null>(null);
  const [filterType, setFilterType] = useState("all");
  const [filterSeverity, setFilterSeverity] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [allIssues, setAllIssues] = useState<RoadIssue[]>([]); // Store all issues
  const [filteredIssues, setFilteredIssues] = useState<RoadIssue[]>([]); // Store filtered issues
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  // Load real data from Supabase
  useEffect(() => {
    const loadIssues = async () => {
      try {
        console.log('🗺️ Loading real data from Supabase...');
        setError(null);

        // Initialize data service first
        await dataService.initialize();

        // Check service status
        const status = dataService.getStatus();
        console.log('📊 DataService status:', status);

        const data = await dataService.getIssues();
        console.log('✅ Loaded issues:', data.length, data);
        setAllIssues(data);
        setFilteredIssues(data); // Initially show all issues
      } catch (error) {
        console.error('❌ Failed to load issues:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setError(errorMessage);
        setAllIssues([]); // Clear any existing data
        setFilteredIssues([]);
      } finally {
        setLoading(false);
      }
    };

    loadIssues();
  }, []);

  // Filter issues when filters change
  useEffect(() => {
    let filtered = [...allIssues];

    // Filter by type
    if (filterType !== "all") {
      filtered = filtered.filter(issue => issue.type === filterType);
    }

    // Filter by severity
    if (filterSeverity !== "all") {
      filtered = filtered.filter(issue => issue.severity === filterSeverity);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(issue =>
        issue.location.toLowerCase().includes(query) ||
        issue.description.toLowerCase().includes(query) ||
        issue.reportedBy.toLowerCase().includes(query)
      );
    }

    setFilteredIssues(filtered);
    console.log(`🔍 Applied filters: ${filtered.length}/${allIssues.length} issues shown`);
  }, [allIssues, filterType, filterSeverity, searchQuery]);

  const handleIssueClick = (issue: RoadIssue) => {
    setSelectedIssue(issue);
  };

  const handleClearFilters = () => {
    setFilterType("all");
    setFilterSeverity("all");
    setSearchQuery("");
  };

  const handleSettingsClick = () => {
    setShowSettings(!showSettings);
  };

  // Helper functions for styling (keeping the better implementations below)

  const getIssueIcon = (type: string) => {
    const iconMap = {
      pothole: AlertTriangle,
      construction: Construction,
      flooding: Droplets,
      accident: Car
    };
    return iconMap[type as keyof typeof iconMap] || AlertTriangle;
  };

  const getSeverityColor = (severity: string) => {
    const colorMap = {
      low: 'bg-green-500',
      medium: 'bg-yellow-500',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };
    return colorMap[severity as keyof typeof colorMap] || 'bg-gray-500';
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      open: 'bg-red-100 text-red-800',
      'in-progress': 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800'
    };
    return colorMap[status as keyof typeof colorMap] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="text-road-charcoal-600"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div className="flex items-center gap-2">
              <MapPin className="w-5 h-5 text-road-blue-600" />
              <h1 className="text-xl font-bold text-road-charcoal-900">Interactive Map</h1>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Layers className="w-4 h-4 mr-1" />
              Layers
            </Button>
            <Button
              onClick={() => navigate("/route-planning")}
              className="bg-zimbabwe-gold-500 hover:bg-zimbabwe-gold-600 text-white"
              size="sm"
            >
              <Route className="w-4 h-4 mr-1" />
              Plan Route
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSettingsClick}
              className={showSettings ? "bg-gray-100" : ""}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-gray-50 border-b border-gray-200 p-4">
          <div className="max-w-4xl mx-auto">
            <h3 className="font-semibold text-gray-900 mb-3">Map Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Display Options</h4>
                <div className="space-y-1">
                  <label className="flex items-center text-sm">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    Show incident markers
                  </label>
                  <label className="flex items-center text-sm">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    Show SOS alerts
                  </label>
                  <label className="flex items-center text-sm">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    Show police locations
                  </label>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Notifications</h4>
                <div className="space-y-1">
                  <label className="flex items-center text-sm">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    New incidents nearby
                  </label>
                  <label className="flex items-center text-sm">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    SOS requests
                  </label>
                  <label className="flex items-center text-sm">
                    <input type="checkbox" className="mr-2" />
                    Traffic updates
                  </label>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Data Source</h4>
                <div className="text-sm text-gray-600">
                  <p>✅ Supabase Connected</p>
                  <p>📊 {allIssues.length} total issues loaded</p>
                  <p>🔄 Real-time updates active</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex h-[calc(100vh-73px)]">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Filters */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h2 className="font-semibold text-road-charcoal-900">Filters & Search</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Clear All
              </Button>
            </div>

            <div className="space-y-3">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search locations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Issue Type</label>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="pothole">Potholes</SelectItem>
                    <SelectItem value="construction">Construction</SelectItem>
                    <SelectItem value="flooding">Flooding</SelectItem>
                    <SelectItem value="accident">Accidents</SelectItem>
                    <SelectItem value="police">Police Locations</SelectItem>
                    <SelectItem value="camera">Camera Locations</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Severity</label>
                <Select value={filterSeverity} onValueChange={setFilterSeverity}>
                  <SelectTrigger>
                    <SelectValue placeholder="All severities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filter Status */}
              <div className="pt-3 border-t border-gray-100">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    Showing {filteredIssues.length} of {allIssues.length} issues
                  </span>
                  {(filterType !== "all" || filterSeverity !== "all" || searchQuery.trim()) && (
                    <Badge variant="secondary" className="text-xs">
                      Filtered
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Issue Details */}
          <div className="flex-1 overflow-y-auto">
            {selectedIssue ? (
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-road-charcoal-900">Issue Details</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedIssue(null)}
                    className="text-gray-500 hover:text-gray-700 h-6 w-6 p-0"
                  >
                    ✕
                  </Button>
                </div>
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      {(() => {
                        const IconComponent = getIssueIcon(selectedIssue.type);
                        return <IconComponent className="w-4 h-4" />;
                      })()}
                      <CardTitle className="text-base">{selectedIssue.location}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex gap-2">
                      <Badge className={getSeverityColor(selectedIssue.severity) + ' text-white text-xs'}>
                        {selectedIssue.severity.toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className={getStatusColor(selectedIssue.status) + ' text-xs'}>
                        {selectedIssue.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                      {selectedIssue.verified && (
                        <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                          VERIFIED
                        </Badge>
                      )}
                    </div>

                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-1">Description</h4>
                      <p className="text-sm text-gray-600">{selectedIssue.description}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-1">Reported By</h4>
                      <p className="text-sm text-gray-600">{selectedIssue.reportedBy}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-1">Reported At</h4>
                      <p className="text-sm text-gray-600">
                        {new Date(selectedIssue.reportedAt).toLocaleDateString()} at{' '}
                        {new Date(selectedIssue.reportedAt).toLocaleTimeString()}
                      </p>
                    </div>

                    <div className="pt-2">
                      <Button className="w-full" size="sm">
                        View Full Report
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="p-4">
                <h3 className="font-semibold text-road-charcoal-900 mb-3">Recent Issues</h3>
                <div className="space-y-2">
                  {filteredIssues.slice(0, 10).map((issue) => (
                    <Card
                      key={issue.id}
                      className="cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => setSelectedIssue(issue)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium capitalize text-sm">{issue.type}</span>
                          <Badge
                            variant={issue.severity === 'high' ? 'destructive' :
                                    issue.severity === 'medium' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {issue.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 truncate">{issue.location}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          By {issue.reportedBy}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                  {filteredIssues.length === 0 && (
                    <div className="text-center text-gray-500 py-8">
                      <MapPin className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No issues match your filters</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleClearFilters}
                        className="mt-2"
                      >
                        Clear Filters
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Map */}
        <div className="flex-1">
          {loading ? (
            <div className="flex items-center justify-center h-full bg-gray-100">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-gray-600">Loading real data from Supabase...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full bg-red-50">
              <div className="text-center p-8 max-w-md">
                <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
                <h3 className="text-lg font-semibold text-red-700 mb-2">Data Loading Error</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  Retry
                </Button>
                <p className="text-sm text-gray-500 mt-4">
                  Please check your internet connection and Supabase configuration.
                </p>
              </div>
            </div>
          ) : (
            <InteractiveMap
              height="100%"
              center={[-17.8292, 31.0522]}
              zoom={13}
              issues={filteredIssues}
              showControls={true}
              onIssueClick={handleIssueClick}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Map;
