/**
 * Data Service for RoadPulse
 * Manages the transition from mock data to real API data
 * Provides a unified interface for data access
 */

import { RoadIssue } from '../components/InteractiveMap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SOSHelper } from '../types/sos';
import { apiService, IssueFilters, CreateIssueRequest, CreateSOSRequest } from './api';
import { realtimeService } from './realtime';
import { supabaseService } from './supabaseService';

// Configuration for development/production modes
const DATA_CONFIG = {
  useSupabase: import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY,
  useMockData: import.meta.env.VITE_USE_MOCK_DATA === 'true' || (!import.meta.env.VITE_API_URL && !import.meta.env.VITE_SUPABASE_URL),
  enableRealtime: import.meta.env.VITE_ENABLE_REALTIME !== 'false',
  mockDataDelay: 500, // Simulate network delay in development
};

// Mock data with realistic Harare CBD positions
const MOCK_ISSUES: RoadIssue[] = [
  // Road Issues
  {
    id: '1',
    type: 'pothole',
    location: 'Samora Machel Avenue & Jason Moyo',
    coordinates: [-17.8292, 31.0522],
    severity: 'high',
    description: 'Large pothole causing traffic delays',
    reportedBy: 'John Mukamuri',
    reportedAt: '2024-01-15T10:30:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '2',
    type: 'construction',
    location: 'First Street & Nelson Mandela Avenue',
    coordinates: [-17.8250, 31.0600],
    severity: 'medium',
    description: 'Road maintenance work in progress',
    reportedBy: 'City Council',
    reportedAt: '2024-01-14T08:00:00Z',
    status: 'in-progress',
    verified: true
  },
  {
    id: '3',
    type: 'accident',
    location: 'Second Street & Speke Avenue',
    coordinates: [-17.8270, 31.0580],
    severity: 'high',
    description: 'Minor vehicle collision, traffic affected',
    reportedBy: 'Traffic Police',
    reportedAt: '2024-01-15T14:20:00Z',
    status: 'open',
    verified: true
  },

  // Police Locations in Harare CBD
  {
    id: '10',
    type: 'police',
    location: 'Samora Machel & First Street Intersection',
    coordinates: [-17.8280, 31.0510],
    severity: 'medium',
    description: 'Traffic police checkpoint - speed enforcement',
    reportedBy: 'Citizen Alert',
    reportedAt: '2024-01-17T08:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T08:30:00Z',
    policeType: 'traffic'
  },
  {
    id: '11',
    type: 'police',
    location: 'Robert Mugabe Road & Second Street',
    coordinates: [-17.8260, 31.0485],
    severity: 'low',
    description: 'Mobile police patrol unit',
    reportedBy: 'Traffic Monitor',
    reportedAt: '2024-01-17T09:15:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T09:15:00Z',
    policeType: 'patrol'
  },
  {
    id: '12',
    type: 'police',
    location: 'Julius Nyerere Way & Angwa Street',
    coordinates: [-17.8300, 31.0540],
    severity: 'high',
    description: 'Police roadblock - document checks',
    reportedBy: 'Motorist Report',
    reportedAt: '2024-01-17T07:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T10:00:00Z',
    policeType: 'checkpoint'
  },
  {
    id: '13',
    type: 'police',
    location: 'Chinhoyi Street & Union Avenue',
    coordinates: [-17.8320, 31.0470],
    severity: 'medium',
    description: 'Traffic police monitoring intersection',
    reportedBy: 'Community Watch',
    reportedAt: '2024-01-17T11:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T11:30:00Z',
    policeType: 'traffic'
  },
  {
    id: '14',
    type: 'police',
    location: 'Kwame Nkrumah Avenue & Baker Avenue',
    coordinates: [-17.8240, 31.0520],
    severity: 'low',
    description: 'Police patrol vehicle parked',
    reportedBy: 'Local Business',
    reportedAt: '2024-01-17T12:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T12:00:00Z',
    policeType: 'patrol'
  },
  {
    id: '15',
    type: 'police',
    location: 'Leopold Takawira Street & Rezende Street',
    coordinates: [-17.8310, 31.0500],
    severity: 'medium',
    description: 'Police checkpoint near market area',
    reportedBy: 'Market Vendor',
    reportedAt: '2024-01-17T13:15:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T13:15:00Z',
    policeType: 'checkpoint'
  },

  // Speed Cameras
  {
    id: '20',
    type: 'camera',
    location: 'Samora Machel Avenue Speed Camera',
    coordinates: [-17.8285, 31.0515],
    severity: 'medium',
    description: 'Fixed speed enforcement camera - 60km/h limit',
    reportedBy: 'City Traffic Dept',
    reportedAt: '2024-01-10T00:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    cameraType: 'speed'
  },
  {
    id: '21',
    type: 'camera',
    location: 'Robert Mugabe Road Traffic Camera',
    coordinates: [-17.8250, 31.0490],
    severity: 'low',
    description: 'Traffic monitoring camera at intersection',
    reportedBy: 'City Traffic Dept',
    reportedAt: '2024-01-10T00:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    cameraType: 'traffic'
  },

  // SOS Alerts in CBD
  {
    id: '30',
    type: 'sos',
    location: 'Samora Machel Avenue near TM Hypermarket',
    coordinates: [-17.8292, 31.0522],
    severity: 'critical',
    description: 'Medical emergency - passenger feeling unwell',
    reportedBy: 'Tendai Mukamuri',
    reportedAt: '2024-01-15T18:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'medical',
    helpNeeded: 'Passenger feeling unwell, need medical help',
    urgencyLevel: 'critical',
    estimatedHelpers: 5,
    responseTime: 8,
    userProfile: {
      name: 'Tendai Mukamuri',
      phone: '+263 77 123 4567',
      rating: 4.9,
      trustScore: 95,
      profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      vehicleInfo: 'Blue Honda Fit (XYZ-789Z)'
    }
  },
  {
    id: '31',
    type: 'sos',
    location: 'First Street near Eastgate Mall',
    coordinates: [-17.8245, 31.0595],
    severity: 'medium',
    description: 'Vehicle breakdown - engine overheating',
    reportedBy: 'Grace Nyathi',
    reportedAt: '2024-01-15T17:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'breakdown',
    helpNeeded: 'Engine overheating, need towing or mechanic assistance',
    urgencyLevel: 'medium',
    estimatedHelpers: 3,
    responseTime: 15,
    userProfile: {
      name: 'Grace Nyathi',
      phone: '+263 78 456 7890',
      rating: 4.6,
      trustScore: 88,
      profileImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      vehicleInfo: 'Red Nissan March (DEF-456Z)'
    }
  },
  {
    id: '32',
    type: 'sos',
    location: 'Julius Nyerere Way near Parliament',
    coordinates: [-17.8305, 31.0535],
    severity: 'medium',
    description: 'Flat tire - need spare tire assistance',
    reportedBy: 'Michael Chivasa',
    reportedAt: '2024-01-15T16:15:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'tire',
    helpNeeded: 'Front tire punctured, need help changing to spare tire',
    urgencyLevel: 'medium',
    estimatedHelpers: 2,
    responseTime: 12,
    userProfile: {
      name: 'Michael Chivasa',
      phone: '+263 77 234 5678',
      rating: 4.7,
      trustScore: 90,
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      vehicleInfo: 'Silver Mazda Demio (GHI-789Z)'
    }
  },
  {
    id: '33',
    type: 'sos',
    location: 'Chinhoyi Street near Mbare Musika',
    coordinates: [-17.8325, 31.0465],
    severity: 'high',
    description: 'Out of fuel - need fuel assistance',
    reportedBy: 'James Moyo',
    reportedAt: '2024-01-15T19:45:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'fuel',
    helpNeeded: 'Ran out of petrol, need someone to bring fuel or towing to nearest station',
    urgencyLevel: 'high',
    estimatedHelpers: 4,
    responseTime: 20,
    userProfile: {
      name: 'James Moyo',
      phone: '+263 78 987 6543',
      rating: 4.5,
      trustScore: 85,
      profileImage: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
      vehicleInfo: 'White Toyota Vitz (JKL-123Z)'
    }
  },
  {
    id: '34',
    type: 'sos',
    location: 'Robert Mugabe Road near University of Zimbabwe',
    coordinates: [-17.8180, 31.0420],
    severity: 'critical',
    description: 'Security concern - suspicious activity',
    reportedBy: 'Sarah Mutasa',
    reportedAt: '2024-01-15T20:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'security',
    helpNeeded: 'Feeling unsafe, suspicious individuals following vehicle',
    urgencyLevel: 'critical',
    estimatedHelpers: 6,
    responseTime: 5,
    userProfile: {
      name: 'Sarah Mutasa',
      phone: '+263 77 345 6789',
      rating: 4.8,
      trustScore: 92,
      profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      vehicleInfo: 'Black Honda Civic (MNO-456Z)'
    }
  }
];

const MOCK_SOS_HELPERS: SOSHelper[] = [
  {
    id: "1",
    name: "John Mukamuri",
    trustScore: 95,
    location: { lat: -17.8292, lng: 31.0522 },
    distance: 1.2,
    responseTime: 180,
    rating: 4.8,
    assistanceCount: 23,
    eta: 8,
    badges: [
      {
        type: 'gold_guardian',
        title: 'Gold Guardian',
        description: 'Completed 20+ successful assists',
        earnedDate: new Date('2024-01-15')
      }
    ]
  }
];

class DataService {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private isInitialized = false;

  /**
   * Initialize the data service
   */
  async initialize(userId?: string): Promise<void> {
    if (this.isInitialized) return;

    console.log(`🔧 Initializing DataService (Supabase: ${DATA_CONFIG.useSupabase}, Mock: ${DATA_CONFIG.useMockData})`);

    if (DATA_CONFIG.useSupabase) {
      try {
        // Test Supabase connection by fetching incidents
        console.log('🔗 Testing Supabase connection...');
        const testData = await supabaseService.getActiveIncidents();
        console.log('✅ Supabase connection established, fetched', testData.length, 'incidents');

        // Initialize real-time subscriptions if enabled
        if (DATA_CONFIG.enableRealtime) {
          this.setupSupabaseRealtimeListeners();
          console.log('✅ Supabase real-time subscriptions active');
        }
      } catch (error) {
        console.warn('⚠️ Supabase connection failed, falling back to mock data:', error);
        console.warn('⚠️ Error details:', error.message || error);
        DATA_CONFIG.useMockData = true;
        DATA_CONFIG.useSupabase = false;
      }
    } else if (!DATA_CONFIG.useMockData) {
      try {
        // Test API connection
        await apiService.healthCheck();
        console.log('✅ API connection established');

        // Initialize real-time connection if enabled
        if (DATA_CONFIG.enableRealtime && userId) {
          await realtimeService.connect(userId);
          this.setupRealtimeListeners();
          console.log('✅ Real-time service connected');
        }
      } catch (error) {
        console.warn('⚠️ API connection failed, falling back to mock data:', error);
        DATA_CONFIG.useMockData = true;
      }
    }

    this.isInitialized = true;
  }

  /**
   * Get road issues with optional filters
   */
  async getIssues(filters: IssueFilters = {}): Promise<RoadIssue[]> {
    const cacheKey = `issues_${JSON.stringify(filters)}`;

    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    let issues: RoadIssue[];

    if (DATA_CONFIG.useSupabase) {
      // Use Supabase for real data
      console.log('📊 Fetching issues from Supabase...');
      const incidents = await supabaseService.getActiveIncidents();
      const sosRequests = await supabaseService.getActiveSOSRequests();

      // Combine incidents and SOS requests
      const sosAsIssues: RoadIssue[] = sosRequests.map(sos => ({
        id: sos.id,
        type: 'sos' as const,
        location: sos.location.address || 'Unknown location',
        coordinates: [sos.location.lat, sos.location.lng],
        severity: this.mapUrgencyToSeverity(sos.urgencyLevel),
        description: sos.description,
        reportedBy: sos.userProfile?.name || 'Unknown user',
        reportedAt: sos.timestamp.toISOString(),
        status: sos.status,
        verified: true,
        isActive: sos.status === 'active',
        sosType: sos.category,
        helpNeeded: sos.helpNeeded,
        urgencyLevel: sos.urgencyLevel,
        estimatedHelpers: sos.estimatedHelpers,
        userProfile: sos.userProfile,
      }));

      issues = [...incidents, ...sosAsIssues];
      console.log(`✅ Loaded ${incidents.length} incidents + ${sosRequests.length} SOS requests from Supabase`);
    } else if (DATA_CONFIG.useMockData) {
      // Simulate API delay
      await this.delay(DATA_CONFIG.mockDataDelay);
      issues = this.filterMockIssues(MOCK_ISSUES, filters);
    } else {
      issues = await apiService.getIssues(filters);
    }

    // Apply filters if using Supabase (mock data filtering is handled separately)
    if (DATA_CONFIG.useSupabase) {
      issues = this.filterIssues(issues, filters);
    }

    // Cache for 30 seconds
    this.setCache(cacheKey, issues, 30000);
    return issues;
  }

  /**
   * Get nearby issues based on location
   */
  async getNearbyIssues(lat: number, lng: number, radius = 5): Promise<RoadIssue[]> {
    return this.getIssues({ lat, lng, radius });
  }

  /**
   * Create a new road issue
   */
  async createIssue(issueData: CreateIssueRequest): Promise<RoadIssue> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      const newIssue: RoadIssue = {
        id: Date.now().toString(),
        type: issueData.type,
        location: issueData.location_name,
        coordinates: issueData.coordinates,
        severity: issueData.severity,
        description: issueData.description,
        reportedBy: 'Current User',
        reportedAt: new Date().toISOString(),
        status: 'open',
        verified: false,
        ...(issueData.sos_type && {
          sosType: issueData.sos_type,
          helpNeeded: issueData.help_needed,
          urgencyLevel: issueData.urgency_level,
          isActive: true,
          estimatedHelpers: 0,
          responseTime: 0,
        })
      };

      // Add to mock data
      MOCK_ISSUES.unshift(newIssue);
      this.clearCache('issues_');
      
      return newIssue;
    } else {
      const issue = await apiService.createIssue(issueData);
      this.clearCache('issues_');
      return issue;
    }
  }

  /**
   * Create SOS request
   */
  async createSOS(sosData: CreateSOSRequest): Promise<SOSAlert> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      const sosAlert: SOSAlert = {
        id: Date.now().toString(),
        userId: 'current_user',
        location: sosData.location,
        timestamp: new Date(),
        status: 'active',
        riskScore: 0.3,
        fraudProbability: 0.1,
        description: sosData.description,
        category: sosData.category,
        helpers: [],
        escalationHistory: []
      };

      return sosAlert;
    } else {
      return apiService.createSOS(sosData);
    }
  }

  /**
   * Get active SOS requests
   */
  async getActiveSOSRequests(lat?: number, lng?: number, radius = 10): Promise<SOSAlert[]> {
    if (DATA_CONFIG.useSupabase) {
      console.log('🆘 Fetching SOS requests from Supabase...');
      return await supabaseService.getActiveSOSRequests();
    } else if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);

      // Filter mock SOS issues
      const sosIssues = MOCK_ISSUES.filter(issue =>
        issue.type === 'sos' && issue.isActive
      );

      return sosIssues.map(issue => ({
        id: issue.id,
        userId: 'mock_user',
        location: {
          lat: issue.coordinates[0],
          lng: issue.coordinates[1],
          accuracy: 10,
          address: issue.location
        },
        timestamp: new Date(issue.reportedAt),
        status: 'active' as const,
        riskScore: 0.3,
        fraudProbability: 0.1,
        description: issue.description,
        category: (issue.sosType || 'other') as SOSAlert['category'],
        helpers: [],
        escalationHistory: []
      }));
    } else {
      return apiService.getActiveSOSRequests(lat, lng, radius);
    }
  }

  /**
   * Get nearby helpers
   */
  async getNearbyHelpers(lat: number, lng: number, radius = 5): Promise<SOSHelper[]> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      return MOCK_SOS_HELPERS.filter(helper => 
        helper.distance <= radius
      );
    } else {
      return apiService.getNearbyHelpers(lat, lng, radius);
    }
  }

  /**
   * Update user location
   */
  async updateLocation(location: {
    lat: number;
    lng: number;
    accuracy: number;
    heading?: number;
    speed?: number;
  }): Promise<void> {
    if (DATA_CONFIG.useMockData) {
      // In mock mode, just update real-time service if connected
      if (realtimeService.isConnected()) {
        realtimeService.updateLocation(location);
      }
      return;
    } else {
      await apiService.updateLocation(location);
    }
  }

  /**
   * Verify an issue
   */
  async verifyIssue(issueId: string, verification: {
    type: 'confirm' | 'dispute' | 'update';
    notes?: string;
  }): Promise<void> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      const issue = MOCK_ISSUES.find(i => i.id === issueId);
      if (issue) {
        issue.verified = verification.type === 'confirm';
      }
      
      this.clearCache('issues_');
      return;
    } else {
      await apiService.verifyIssue(issueId, verification);
      this.clearCache('issues_');
    }
  }

  /**
   * Setup real-time event listeners
   */
  private setupRealtimeListeners(): void {
    // Clear cache when new issues are created
    realtimeService.on('issue:created', () => {
      this.clearCache('issues_');
    });

    realtimeService.on('issue:verified', () => {
      this.clearCache('issues_');
    });

    realtimeService.on('issue:resolved', () => {
      this.clearCache('issues_');
    });
  }

  /**
   * Setup Supabase real-time listeners
   */
  private setupSupabaseRealtimeListeners(): void {
    // Subscribe to new incidents
    supabaseService.subscribeToIncidents((incident) => {
      console.log('🆕 New incident received via real-time:', incident);
      this.clearCache('issues_');
    });

    // Subscribe to new SOS requests
    supabaseService.subscribeToSOSRequests((sosAlert) => {
      console.log('🆕 New SOS request received via real-time:', sosAlert);
      this.clearCache('issues_');
    });
  }

  /**
   * Map urgency level to severity
   */
  private mapUrgencyToSeverity(urgency: string): 'low' | 'medium' | 'high' | 'critical' {
    switch (urgency) {
      case 'critical': return 'critical';
      case 'high': return 'high';
      case 'medium': return 'medium';
      case 'low': return 'low';
      default: return 'medium';
    }
  }

  /**
   * Filter issues (for Supabase data)
   */
  private filterIssues(issues: RoadIssue[], filters: IssueFilters): RoadIssue[] {
    let filtered = [...issues];

    if (filters.type && filters.type !== 'all') {
      filtered = filtered.filter(issue => issue.type === filters.type);
    }

    if (filters.severity && filters.severity !== 'all') {
      filtered = filtered.filter(issue => issue.severity === filters.severity);
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(issue => issue.status === filters.status);
    }

    if (filters.lat && filters.lng && filters.radius) {
      filtered = filtered.filter(issue => {
        const distance = this.calculateDistance(
          filters.lat!, filters.lng!,
          issue.coordinates[0], issue.coordinates[1]
        );
        return distance <= filters.radius!;
      });
    }

    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  }

  /**
   * Cache management
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private clearCache(keyPrefix: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(keyPrefix)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Filter mock issues based on filters
   */
  private filterMockIssues(issues: RoadIssue[], filters: IssueFilters): RoadIssue[] {
    let filtered = [...issues];

    if (filters.type && filters.type !== 'all') {
      filtered = filtered.filter(issue => issue.type === filters.type);
    }

    if (filters.severity && filters.severity !== 'all') {
      filtered = filtered.filter(issue => issue.severity === filters.severity);
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(issue => issue.status === filters.status);
    }

    if (filters.lat && filters.lng && filters.radius) {
      filtered = filtered.filter(issue => {
        const distance = this.calculateDistance(
          filters.lat!, filters.lng!,
          issue.coordinates[0], issue.coordinates[1]
        );
        return distance <= filters.radius!;
      });
    }

    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  }

  /**
   * Calculate distance between two points (Haversine formula)
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get service status
   */
  getStatus(): {
    useSupabase: boolean;
    useMockData: boolean;
    enableRealtime: boolean;
    isInitialized: boolean;
    realtimeConnected: boolean;
  } {
    return {
      useSupabase: DATA_CONFIG.useSupabase,
      useMockData: DATA_CONFIG.useMockData,
      enableRealtime: DATA_CONFIG.enableRealtime,
      isInitialized: this.isInitialized,
      realtimeConnected: realtimeService.isConnected(),
    };
  }
}

// Export singleton instance
export const dataService = new DataService();

// Export for testing
export { DataService };
