/**
 * Data Service for RoadPulse
 * Manages the transition from mock data to real API data
 * Provides a unified interface for data access
 */

import { RoadIssue } from '../components/InteractiveMap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>elper } from '../types/sos';
import { apiService, IssueFilters, CreateIssueRequest, CreateSOSRequest } from './api';
import { realtimeService } from './realtime';
import { supabaseService } from './supabaseService';

// Configuration for production mode - no mock data fallbacks
const DATA_CONFIG = {
  useSupabase: !!(import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY),
  useAPI: !!import.meta.env.VITE_API_URL,
  enableRealtime: import.meta.env.VITE_ENABLE_REALTIME !== 'false',
};

// No mock data - using real data sources only

class DataService {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private isInitialized = false;

  /**
   * Initialize the data service
   */
  async initialize(userId?: string): Promise<void> {
    if (this.isInitialized) return;

    console.log(`🔧 Initializing DataService (Supabase: ${DATA_CONFIG.useSupabase}, API: ${DATA_CONFIG.useAPI})`);

    if (!DATA_CONFIG.useSupabase && !DATA_CONFIG.useAPI) {
      throw new Error(
        'No data source configured. Please set either VITE_SUPABASE_URL + VITE_SUPABASE_ANON_KEY or VITE_API_URL environment variables.'
      );
    }

    if (DATA_CONFIG.useSupabase) {
      try {
        // Test Supabase connection by fetching incidents
        console.log('🔗 Testing Supabase connection...');
        const testData = await supabaseService.getActiveIncidents();
        console.log('✅ Supabase connection established, fetched', testData.length, 'incidents');

        // Initialize real-time subscriptions if enabled
        if (DATA_CONFIG.enableRealtime) {
          this.setupSupabaseRealtimeListeners();
          console.log('✅ Supabase real-time subscriptions active');
        }
      } catch (error) {
        console.error('❌ Supabase initialization failed:', error);
        throw new Error(`Failed to initialize Supabase: ${error.message || error}`);
      }
    } else if (DATA_CONFIG.useAPI) {
      try {
        // Test API connection
        await apiService.healthCheck();
        console.log('✅ API connection established');

        // Initialize real-time connection if enabled
        if (DATA_CONFIG.enableRealtime && userId) {
          await realtimeService.connect(userId);
          this.setupRealtimeListeners();
          console.log('✅ Real-time service connected');
        }
      } catch (error) {
        console.error('❌ API initialization failed:', error);
        throw new Error(`Failed to initialize API: ${error.message || error}`);
      }
    }

    this.isInitialized = true;
  }

  /**
   * Get road issues with optional filters
   */
  async getIssues(filters: IssueFilters = {}): Promise<RoadIssue[]> {
    if (!this.isInitialized) {
      throw new Error('DataService not initialized. Call initialize() first.');
    }

    const cacheKey = `issues_${JSON.stringify(filters)}`;

    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    let issues: RoadIssue[];

    try {
      if (DATA_CONFIG.useSupabase) {
        // Use Supabase for real data
        console.log('📊 Fetching issues from Supabase...');
        const incidents = await supabaseService.getActiveIncidents();
        const sosRequests = await supabaseService.getActiveSOSRequests();

        // Combine incidents and SOS requests
        const sosAsIssues: RoadIssue[] = sosRequests.map(sos => ({
          id: sos.id,
          type: 'sos' as const,
          location: sos.location.address || 'Unknown location',
          coordinates: [sos.location.lat, sos.location.lng],
          severity: this.mapUrgencyToSeverity(sos.urgencyLevel),
          description: sos.description,
          reportedBy: sos.userProfile?.name || 'Unknown user',
          reportedAt: sos.timestamp.toISOString(),
          status: sos.status,
          verified: true,
          isActive: sos.status === 'active',
          sosType: sos.category,
          helpNeeded: sos.helpNeeded,
          urgencyLevel: sos.urgencyLevel,
          estimatedHelpers: sos.estimatedHelpers,
          userProfile: sos.userProfile,
        }));

        issues = [...incidents, ...sosAsIssues];
        console.log(`✅ Loaded ${incidents.length} incidents + ${sosRequests.length} SOS requests from Supabase`);

        // Apply filters
        issues = this.filterIssues(issues, filters);
      } else if (DATA_CONFIG.useAPI) {
        issues = await apiService.getIssues(filters);
      } else {
        throw new Error('No data source available');
      }

      // Cache for 30 seconds
      this.setCache(cacheKey, issues, 30000);
      return issues;
    } catch (error) {
      console.error('❌ Failed to fetch issues:', error);
      throw new Error(`Failed to fetch road issues: ${error.message || error}`);
    }
  }

  /**
   * Get nearby issues based on location
   */
  async getNearbyIssues(lat: number, lng: number, radius = 5): Promise<RoadIssue[]> {
    return this.getIssues({ lat, lng, radius });
  }

  /**
   * Create a new road issue
   */
  async createIssue(issueData: CreateIssueRequest): Promise<RoadIssue> {
    if (!this.isInitialized) {
      throw new Error('DataService not initialized. Call initialize() first.');
    }

    try {
      let issue: RoadIssue;

      if (DATA_CONFIG.useSupabase) {
        // Use Supabase to create issue
        issue = await supabaseService.createIncident({
          type: issueData.type,
          location: issueData.coordinates,
          address: issueData.location_name,
          severity: issueData.severity,
          description: issueData.description,
        });
      } else if (DATA_CONFIG.useAPI) {
        issue = await apiService.createIssue(issueData);
      } else {
        throw new Error('No data source available');
      }

      this.clearCache('issues_');
      return issue;
    } catch (error) {
      console.error('❌ Failed to create issue:', error);
      throw new Error(`Failed to create road issue: ${error.message || error}`);
    }
  }

  /**
   * Add a new road issue (simplified interface for quick reporting)
   */
  async addIssue(issueData: {
    type: 'pothole' | 'construction' | 'accident' | 'police' | 'camera';
    location: string;
    coordinates: [number, number];
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    reportedBy: string;
    timestamp: string;
    status: 'active';
    verified: boolean;
    upvotes: number;
    downvotes: number;
  }): Promise<RoadIssue> {
    console.log('📝 Adding new issue via simplified interface:', issueData);

    // Convert to CreateIssueRequest format
    const createRequest: CreateIssueRequest = {
      type: issueData.type,
      coordinates: issueData.coordinates,
      location_name: issueData.location,
      severity: issueData.severity,
      description: issueData.description,
      reporter_name: issueData.reportedBy,
    };

    return this.createIssue(createRequest);
  }

  /**
   * Create SOS request
   */
  async createSOS(sosData: CreateSOSRequest): Promise<SOSAlert> {
    if (!this.isInitialized) {
      throw new Error('DataService not initialized. Call initialize() first.');
    }

    try {
      if (DATA_CONFIG.useSupabase) {
        return await supabaseService.createSOSRequest({
          sos_type: sosData.category,
          urgency_level: sosData.urgency_level || 'medium',
          location: [sosData.location.lat, sosData.location.lng],
          address: sosData.location.address,
          description: sosData.description,
          help_needed: sosData.help_needed,
          estimated_helpers: sosData.estimated_helpers || 1,
        });
      } else if (DATA_CONFIG.useAPI) {
        return await apiService.createSOS(sosData);
      } else {
        throw new Error('No data source available');
      }
    } catch (error) {
      console.error('❌ Failed to create SOS request:', error);
      throw new Error(`Failed to create SOS request: ${error.message || error}`);
    }
  }

  /**
   * Get active SOS requests
   */
  async getActiveSOSRequests(lat?: number, lng?: number, radius = 10): Promise<SOSAlert[]> {
    if (!this.isInitialized) {
      throw new Error('DataService not initialized. Call initialize() first.');
    }

    try {
      if (DATA_CONFIG.useSupabase) {
        console.log('🆘 Fetching SOS requests from Supabase...');
        return await supabaseService.getActiveSOSRequests();
      } else if (DATA_CONFIG.useAPI) {
        return await apiService.getActiveSOSRequests(lat, lng, radius);
      } else {
        throw new Error('No data source available');
      }
    } catch (error) {
      console.error('❌ Failed to fetch SOS requests:', error);
      throw new Error(`Failed to fetch SOS requests: ${error.message || error}`);
    }
  }

  /**
   * Get nearby helpers
   */
  async getNearbyHelpers(lat: number, lng: number, radius = 5): Promise<SOSHelper[]> {
    if (!this.isInitialized) {
      throw new Error('DataService not initialized. Call initialize() first.');
    }

    try {
      if (DATA_CONFIG.useAPI) {
        return await apiService.getNearbyHelpers(lat, lng, radius);
      } else {
        throw new Error('Helper functionality requires API backend');
      }
    } catch (error) {
      console.error('❌ Failed to fetch nearby helpers:', error);
      throw new Error(`Failed to fetch nearby helpers: ${error.message || error}`);
    }
  }

  /**
   * Update user location
   */
  async updateLocation(location: {
    lat: number;
    lng: number;
    accuracy: number;
    heading?: number;
    speed?: number;
  }): Promise<void> {
    if (DATA_CONFIG.useMockData) {
      // In mock mode, just update real-time service if connected
      if (realtimeService.isConnected()) {
        realtimeService.updateLocation(location);
      }
      return;
    } else {
      await apiService.updateLocation(location);
    }
  }

  /**
   * Verify an issue
   */
  async verifyIssue(issueId: string, verification: {
    type: 'confirm' | 'dispute' | 'update';
    notes?: string;
  }): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('DataService not initialized. Call initialize() first.');
    }

    try {
      if (DATA_CONFIG.useAPI) {
        await apiService.verifyIssue(issueId, verification);
      } else {
        throw new Error('Issue verification requires API backend');
      }

      this.clearCache('issues_');
    } catch (error) {
      console.error('❌ Failed to verify issue:', error);
      throw new Error(`Failed to verify issue: ${error.message || error}`);
    }
  }

  /**
   * Setup real-time event listeners
   */
  private setupRealtimeListeners(): void {
    // Clear cache when new issues are created
    realtimeService.on('issue:created', () => {
      this.clearCache('issues_');
    });

    realtimeService.on('issue:verified', () => {
      this.clearCache('issues_');
    });

    realtimeService.on('issue:resolved', () => {
      this.clearCache('issues_');
    });
  }

  /**
   * Setup Supabase real-time listeners
   */
  private setupSupabaseRealtimeListeners(): void {
    // Subscribe to new incidents
    supabaseService.subscribeToIncidents((incident) => {
      console.log('🆕 New incident received via real-time:', incident);
      this.clearCache('issues_');
    });

    // Subscribe to new SOS requests
    supabaseService.subscribeToSOSRequests((sosAlert) => {
      console.log('🆕 New SOS request received via real-time:', sosAlert);
      this.clearCache('issues_');
    });
  }

  /**
   * Map urgency level to severity
   */
  private mapUrgencyToSeverity(urgency: string): 'low' | 'medium' | 'high' | 'critical' {
    switch (urgency) {
      case 'critical': return 'critical';
      case 'high': return 'high';
      case 'medium': return 'medium';
      case 'low': return 'low';
      default: return 'medium';
    }
  }

  /**
   * Filter issues (for Supabase data)
   */
  private filterIssues(issues: RoadIssue[], filters: IssueFilters): RoadIssue[] {
    let filtered = [...issues];

    if (filters.type && filters.type !== 'all') {
      filtered = filtered.filter(issue => issue.type === filters.type);
    }

    if (filters.severity && filters.severity !== 'all') {
      filtered = filtered.filter(issue => issue.severity === filters.severity);
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(issue => issue.status === filters.status);
    }

    if (filters.lat && filters.lng && filters.radius) {
      filtered = filtered.filter(issue => {
        const distance = this.calculateDistance(
          filters.lat!, filters.lng!,
          issue.coordinates[0], issue.coordinates[1]
        );
        return distance <= filters.radius!;
      });
    }

    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  }

  /**
   * Cache management
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private clearCache(keyPrefix: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(keyPrefix)) {
        this.cache.delete(key);
      }
    }
  }

  // Mock data filtering removed - using real data sources only

  /**
   * Calculate distance between two points (Haversine formula)
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  // Mock data delay function removed - using real data sources only

  /**
   * Get service status
   */
  getStatus(): {
    useSupabase: boolean;
    useAPI: boolean;
    enableRealtime: boolean;
    isInitialized: boolean;
    realtimeConnected: boolean;
  } {
    return {
      useSupabase: DATA_CONFIG.useSupabase,
      useAPI: DATA_CONFIG.useAPI,
      enableRealtime: DATA_CONFIG.enableRealtime,
      isInitialized: this.isInitialized,
      realtimeConnected: realtimeService.isConnected(),
    };
  }
}

// Export singleton instance
export const dataService = new DataService();

// Export for testing
export { DataService };
