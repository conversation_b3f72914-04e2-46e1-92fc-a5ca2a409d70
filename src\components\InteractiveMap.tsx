import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap, Circle, Polyline } from 'react-leaflet';
import L from 'leaflet';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Construction, Droplets, Car, MapPin, Clock, User, Navigation, Shield, Camera } from 'lucide-react';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

export interface RoadIssue {
  id: string;
  type: 'pothole' | 'construction' | 'flooding' | 'accident' | 'police' | 'camera' | 'sos';
  location: string;
  coordinates: [number, number];
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  reportedBy: string;
  reportedAt: string;
  status: 'open' | 'in-progress' | 'resolved' | 'active';
  verified: boolean;
  // Additional fields for police and cameras
  isActive?: boolean;
  lastSeen?: string;
  cameraType?: 'speed' | 'traffic' | 'security' | 'mobile';
  policeType?: 'traffic' | 'patrol' | 'checkpoint' | 'emergency';
  // SOS/Help request fields
  sosType?: 'breakdown' | 'medical' | 'accident' | 'security' | 'fuel' | 'tire' | 'other';
  helpNeeded?: string;
  userProfile?: {
    name: string;
    phone: string;
    rating: number;
    trustScore: number;
    profileImage?: string;
    vehicleInfo?: string;
  };
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  estimatedHelpers?: number;
  responseTime?: number; // minutes
}

interface InteractiveMapProps {
  height?: string;
  center?: [number, number];
  zoom?: number;
  issues?: RoadIssue[];
  showControls?: boolean;
  onIssueClick?: (issue: RoadIssue) => void;
  mapTheme?: 'standard' | 'satellite' | 'dark' | 'terrain';
  routeData?: any;
  isNavigating?: boolean;
  currentLocation?: {lat: number, lng: number} | null;
  navigationStep?: number;
}

// Enhanced Harare CBD data - now loaded from dataService
const defaultIssues: RoadIssue[] = [
  // Fallback data in case service fails
  {
    id: '1',
    type: 'pothole',
    location: 'Samora Machel Avenue & Jason Moyo',
    coordinates: [-17.8292, 31.0522],
    severity: 'high',
    description: 'Large pothole causing traffic delays',
    reportedBy: 'John Mukamuri',
    reportedAt: '2024-01-15T10:30:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '2',
    type: 'construction',
    location: 'Robert Mugabe Road',
    coordinates: [-17.8206, 31.0492],
    severity: 'medium',
    description: 'Road construction work in progress',
    reportedBy: 'City Council',
    reportedAt: '2024-01-14T08:00:00Z',
    status: 'in-progress',
    verified: true
  },
  {
    id: '3',
    type: 'flooding',
    location: 'Chinhoyi Street',
    coordinates: [-17.8340, 31.0580],
    severity: 'critical',
    description: 'Severe flooding after heavy rains',
    reportedBy: 'Mary Chikwanha',
    reportedAt: '2024-01-16T06:45:00Z',
    status: 'open',
    verified: false
  },
  {
    id: '4',
    type: 'accident',
    location: 'Julius Nyerere Way',
    coordinates: [-17.8180, 31.0470],
    severity: 'high',
    description: 'Traffic accident blocking lane',
    reportedBy: 'Traffic Police',
    reportedAt: '2024-01-16T14:20:00Z',
    status: 'in-progress',
    verified: true
  },
  {
    id: '5',
    type: 'pothole',
    location: 'Harare Drive',
    coordinates: [-17.8400, 31.0600],
    severity: 'medium',
    description: 'Multiple small potholes',
    reportedBy: 'Transport Authority',
    reportedAt: '2024-01-13T16:15:00Z',
    status: 'resolved',
    verified: true
  },
  {
    id: '6',
    type: 'pothole',
    location: 'Leopold Takawira Street',
    coordinates: [-17.8250, 31.0530],
    severity: 'low',
    description: 'Small pothole near intersection',
    reportedBy: 'Citizen Reporter',
    reportedAt: '2024-01-17T09:15:00Z',
    status: 'open',
    verified: false
  },
  {
    id: '7',
    type: 'construction',
    location: 'Borrowdale Road',
    coordinates: [-17.8100, 31.0700],
    severity: 'medium',
    description: 'Bridge maintenance work',
    reportedBy: 'ZINARA',
    reportedAt: '2024-01-12T07:00:00Z',
    status: 'in-progress',
    verified: true
  },
  {
    id: '8',
    type: 'accident',
    location: 'Enterprise Road',
    coordinates: [-17.8450, 31.0450],
    severity: 'critical',
    description: 'Multi-vehicle accident, road closed',
    reportedBy: 'Emergency Services',
    reportedAt: '2024-01-17T15:30:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '9',
    type: 'flooding',
    location: 'Seke Road',
    coordinates: [-17.8500, 31.0800],
    severity: 'high',
    description: 'Road partially flooded, proceed with caution',
    reportedBy: 'Weather Service',
    reportedAt: '2024-01-16T12:00:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '10',
    type: 'pothole',
    location: 'Mazowe Street',
    coordinates: [-17.8320, 31.0480],
    severity: 'medium',
    description: 'Deep pothole affecting both lanes',
    reportedBy: 'Local Resident',
    reportedAt: '2024-01-15T18:45:00Z',
    status: 'open',
    verified: false
  },
  // Police locations
  {
    id: '11',
    type: 'police',
    location: 'Samora Machel & First Street',
    coordinates: [-17.8280, 31.0510],
    severity: 'medium',
    description: 'Traffic police checkpoint - speed enforcement',
    reportedBy: 'Citizen Alert',
    reportedAt: '2024-01-17T08:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T08:30:00Z',
    policeType: 'traffic'
  },
  {
    id: '12',
    type: 'police',
    location: 'Robert Mugabe Road Patrol',
    coordinates: [-17.8200, 31.0485],
    severity: 'low',
    description: 'Mobile police patrol unit',
    reportedBy: 'Traffic Monitor',
    reportedAt: '2024-01-17T09:15:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T09:15:00Z',
    policeType: 'patrol'
  },
  {
    id: '13',
    type: 'police',
    location: 'Enterprise Road Checkpoint',
    coordinates: [-17.8420, 31.0460],
    severity: 'high',
    description: 'Police roadblock - document checks',
    reportedBy: 'Motorist Report',
    reportedAt: '2024-01-17T07:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-17T10:00:00Z',
    policeType: 'checkpoint'
  },
  // Camera locations
  {
    id: '14',
    type: 'camera',
    location: 'Borrowdale Road Speed Camera',
    coordinates: [-17.8120, 31.0680],
    severity: 'medium',
    description: 'Fixed speed enforcement camera - 60km/h limit',
    reportedBy: 'City Traffic Dept',
    reportedAt: '2024-01-10T00:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    cameraType: 'speed'
  },
  {
    id: '15',
    type: 'camera',
    location: 'Julius Nyerere Way Traffic Cam',
    coordinates: [-17.8190, 31.0475],
    severity: 'low',
    description: 'Traffic monitoring camera - intersection surveillance',
    reportedBy: 'Traffic Control',
    reportedAt: '2024-01-08T00:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    cameraType: 'traffic'
  },
  {
    id: '16',
    type: 'camera',
    location: 'Seke Road Mobile Camera',
    coordinates: [-17.8480, 31.0750],
    severity: 'high',
    description: 'Mobile speed camera unit - varies location',
    reportedBy: 'Driver Alert',
    reportedAt: '2024-01-17T11:30:00Z',
    status: 'active',
    verified: false,
    isActive: true,
    lastSeen: '2024-01-17T11:30:00Z',
    cameraType: 'mobile'
  },
  {
    id: '17',
    type: 'camera',
    location: 'Harare Drive Security Cam',
    coordinates: [-17.8380, 31.0590],
    severity: 'low',
    description: 'Security camera monitoring high-crime area',
    reportedBy: 'Security Services',
    reportedAt: '2024-01-05T00:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    cameraType: 'security'
  },
  // SOS Help Requests
  {
    id: '18',
    type: 'sos',
    location: 'Enterprise Road near Borrowdale',
    coordinates: [-17.8150, 31.0650],
    severity: 'high',
    description: 'Vehicle breakdown - need roadside assistance',
    reportedBy: 'Chipo Madziva',
    reportedAt: '2024-01-15T18:45:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'breakdown',
    helpNeeded: 'Car won\'t start, need jump start or towing',
    urgencyLevel: 'high',
    estimatedHelpers: 3,
    responseTime: 15,
    userProfile: {
      name: 'Chipo Madziva',
      phone: '+263 77 123 4567',
      rating: 4.8,
      trustScore: 92,
      profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      vehicleInfo: 'White Toyota Corolla (ABC-123Z)'
    }
  },
  {
    id: '19',
    type: 'sos',
    location: 'Samora Machel Avenue CBD',
    coordinates: [-17.8280, 31.0510],
    severity: 'critical',
    description: 'Medical emergency - need immediate assistance',
    reportedBy: 'Tendai Mukamuri',
    reportedAt: '2024-01-15T19:20:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'medical',
    helpNeeded: 'Passenger feeling unwell, need medical help or transport to hospital',
    urgencyLevel: 'critical',
    estimatedHelpers: 5,
    responseTime: 8,
    userProfile: {
      name: 'Tendai Mukamuri',
      phone: '+263 71 987 6543',
      rating: 4.9,
      trustScore: 95,
      profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      vehicleInfo: 'Blue Honda Fit (XYZ-789Z)'
    }
  },
  {
    id: '20',
    type: 'sos',
    location: 'Chinhoyi Street near Market',
    coordinates: [-17.8320, 31.0470],
    severity: 'medium',
    description: 'Flat tire - need spare tire assistance',
    reportedBy: 'Grace Nyathi',
    reportedAt: '2024-01-15T17:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'tire',
    helpNeeded: 'Front tire punctured, need help changing to spare tire',
    urgencyLevel: 'medium',
    estimatedHelpers: 2,
    responseTime: 20,
    userProfile: {
      name: 'Grace Nyathi',
      phone: '+263 78 456 7890',
      rating: 4.6,
      trustScore: 88,
      profileImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      vehicleInfo: 'Red Nissan March (DEF-456Z)'
    }
  },
  {
    id: '21',
    type: 'sos',
    location: 'Robert Mugabe Road near University',
    coordinates: [-17.8180, 31.0420],
    severity: 'medium',
    description: 'Out of fuel - need fuel assistance',
    reportedBy: 'Michael Chivasa',
    reportedAt: '2024-01-15T16:15:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'fuel',
    helpNeeded: 'Ran out of petrol, need someone to bring fuel or towing to nearest station',
    urgencyLevel: 'medium',
    estimatedHelpers: 4,
    responseTime: 25,
    userProfile: {
      name: 'Michael Chivasa',
      phone: '+263 77 234 5678',
      rating: 4.7,
      trustScore: 90,
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      vehicleInfo: 'Silver Mazda Demio (GHI-789Z)'
    }
  }
];

const getIssueIcon = (issue: RoadIssue) => {
  const iconMap = {
    pothole: AlertTriangle,
    construction: Construction,
    flooding: Droplets,
    accident: Car,
    police: Shield,
    camera: Camera,
    sos: AlertTriangle // Will be customized in NavigationMode
  };
  return iconMap[issue.type];
};

const getSeverityColor = (severity: string) => {
  const colorMap = {
    low: 'bg-green-500',
    medium: 'bg-yellow-500',
    high: 'bg-orange-500',
    critical: 'bg-red-500'
  };
  return colorMap[severity as keyof typeof colorMap] || 'bg-gray-500';
};

const getStatusColor = (status: string) => {
  const colorMap = {
    open: 'bg-red-100 text-red-800',
    'in-progress': 'bg-yellow-100 text-yellow-800',
    resolved: 'bg-green-100 text-green-800'
  };
  return colorMap[status as keyof typeof colorMap] || 'bg-gray-100 text-gray-800';
};

const createCustomIcon = (issue: RoadIssue) => {
  const severityColor = getSeverityColor(issue.severity).replace('bg-', '');
  const colorMap = {
    'green-500': '#10b981',
    'yellow-500': '#f59e0b',
    'orange-500': '#f97316',
    'red-500': '#ef4444'
  };

  const bgColor = colorMap[severityColor as keyof typeof colorMap] || '#6b7280';

  // Create SVG icons for different issue types
  const getIssueIconSVG = (type: string) => {
    switch (type) {
      case 'pothole':
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <!-- Pothole representation with jagged edges -->
            <ellipse cx="14" cy="14" rx="6" ry="4" fill="white" opacity="0.9"/>
            <path d="M8 14c1-1 2 1 3-1s2 1 3-1 2 1 3-1 2 1 3-1 2 1 3-1" stroke="white" strokeWidth="1.5" fill="none"/>
            <circle cx="11" cy="13" r="1" fill="${bgColor}"/>
            <circle cx="17" cy="15" r="1.5" fill="${bgColor}"/>
            <circle cx="14" cy="16" r="0.8" fill="${bgColor}"/>
          </svg>
        `;
      case 'construction':
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <!-- Construction cone -->
            <path d="M14 8l-4 12h8l-4-12z" fill="white"/>
            <rect x="10" y="12" width="8" height="1.5" fill="${bgColor}"/>
            <rect x="10.5" y="15" width="7" height="1.5" fill="${bgColor}"/>
            <rect x="11" y="18" width="6" height="1.5" fill="${bgColor}"/>
            <rect x="13" y="6" width="2" height="2" fill="white"/>
          </svg>
        `;
      case 'flooding':
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <!-- Water waves -->
            <path d="M6 16c2-2 4 0 6-2s4 0 6-2s4 0 6-2v8H6v-2z" fill="white" opacity="0.9"/>
            <path d="M6 12c2-1.5 4 1 6-1s4 1 6-1s4 1 6-1" stroke="white" strokeWidth="1.5" fill="none"/>
            <path d="M6 14c2-1 4 0.5 6-1s4 0.5 6-1s4 0.5 6-1" stroke="white" strokeWidth="1" fill="none" opacity="0.7"/>
            <circle cx="10" cy="18" r="0.5" fill="${bgColor}" opacity="0.6"/>
            <circle cx="18" cy="19" r="0.8" fill="${bgColor}" opacity="0.6"/>
          </svg>
        `;
      case 'accident':
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <!-- Warning triangle with exclamation -->
            <path d="M14 8l-5 10h10l-5-10z" fill="white" stroke="white" strokeWidth="0.5"/>
            <path d="M14 11v4" stroke="${bgColor}" strokeWidth="2" strokeLinecap="round"/>
            <circle cx="14" cy="17" r="1" fill="${bgColor}"/>
            <!-- Small impact lines -->
            <path d="M8 10l2 2M20 10l-2 2M8 18l2-2M20 18l-2-2" stroke="white" strokeWidth="1" opacity="0.6"/>
          </svg>
        `;
      case 'police':
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <!-- Police shield -->
            <path d="M14 6l-6 3v6c0 4 6 7 6 7s6-3 6-7v-6l-6-3z" fill="white" stroke="white" strokeWidth="0.5"/>
            <!-- Badge details -->
            <circle cx="14" cy="13" r="2" fill="${bgColor}"/>
            <path d="M14 10v2M14 14v2M12 12h4" stroke="${bgColor}" strokeWidth="1" strokeLinecap="round"/>
            <!-- Star on badge -->
            <path d="M14 11.5l0.5 1h1l-0.8 0.6 0.3 1-1-0.7-1 0.7 0.3-1-0.8-0.6h1l0.5-1z" fill="white" stroke="none"/>
          </svg>
        `;
      case 'camera':
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <!-- Camera body -->
            <rect x="8" y="11" width="12" height="8" rx="2" fill="white"/>
            <!-- Camera lens -->
            <circle cx="14" cy="15" r="3" fill="${bgColor}"/>
            <circle cx="14" cy="15" r="1.5" fill="white"/>
            <!-- Camera details -->
            <rect x="9" y="9" width="3" height="2" rx="1" fill="white"/>
            <circle cx="18" cy="13" r="0.5" fill="${bgColor}"/>
            <!-- Mounting bracket -->
            <rect x="13" y="19" width="2" height="3" fill="white"/>
            <rect x="11" y="21" width="6" height="1" fill="white"/>
          </svg>
        `;
      default:
        return `
          <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="14" cy="14" r="12" fill="${bgColor}" stroke="white" strokeWidth="2"/>
            <circle cx="14" cy="14" r="4" fill="white"/>
          </svg>
        `;
    }
  };

  return L.divIcon({
    html: `
      <div style="position: relative; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));">
        ${getIssueIconSVG(issue.type)}
      </div>
    `,
    className: 'custom-marker',
    iconSize: [28, 28],
    iconAnchor: [14, 14]
  });
};

// Location control component
const LocationControl = () => {
  const map = useMap();

  const handleLocateUser = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          map.setView([latitude, longitude], 15);

          // Add a marker for user location
          L.marker([latitude, longitude])
            .addTo(map)
            .bindPopup('Your current location')
            .openPopup();
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get your location. Please check your browser settings.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  };

  return (
    <div className="absolute bottom-4 right-4 z-10">
      <Button
        onClick={handleLocateUser}
        size="sm"
        className="bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-md"
      >
        <Navigation className="w-4 h-4" />
      </Button>
    </div>
  );
};

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  height = '400px',
  center = [-17.8292, 31.0522], // Harare, Zimbabwe
  zoom = 13,
  issues = defaultIssues,
  showControls = true,
  onIssueClick,
  mapTheme = 'standard',
  routeData,
  isNavigating = false,
  currentLocation,
  navigationStep = 0
}) => {
  const [selectedIssue, setSelectedIssue] = useState<RoadIssue | null>(null);
  const [mapError, setMapError] = useState<string | null>(null);

  // Debug logging
  useEffect(() => {
    console.log('🗺️ InteractiveMap received issues:', issues.length);
    console.log('📍 First few issues:', issues.slice(0, 3));
    console.log('🗺️ Map props - center:', center, 'zoom:', zoom, 'height:', height, 'theme:', mapTheme);
    if (routeData) {
      console.log('🧭 Route data received:', routeData);
    }
  }, [issues, center, zoom, height, mapTheme, routeData]);

  // Decode polyline geometry from OpenRouteService
  const decodePolyline = (encoded: string): [number, number][] => {
    if (!encoded) return [];

    const coordinates: [number, number][] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const deltaLat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const deltaLng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      coordinates.push([lat / 1e5, lng / 1e5]);
    }

    return coordinates;
  };

  // Get route coordinates for display
  const getRouteCoordinates = (): [number, number][] => {
    if (!routeData) {
      console.log('❌ No route data available');
      return [];
    }

    console.log('🗺️ Processing route geometry:', routeData.geometry);

    if (typeof routeData.geometry === 'string') {
      // Encoded polyline
      console.log('🔗 Decoding polyline geometry');
      return decodePolyline(routeData.geometry);
    } else if (routeData.geometry && routeData.geometry.coordinates) {
      // GeoJSON format - coordinates are [lng, lat], need to flip to [lat, lng]
      console.log('📍 Processing GeoJSON coordinates, count:', routeData.geometry.coordinates.length);
      const coords = routeData.geometry.coordinates.map((coord: [number, number]) => [coord[1], coord[0]] as [number, number]);
      console.log('📍 First few coordinates:', coords.slice(0, 3));
      return coords;
    } else if (routeData.segments && routeData.segments[0] && routeData.segments[0].geometry) {
      // Try to get geometry from segments
      console.log('🔗 Getting geometry from segments');
      const segment = routeData.segments[0];
      if (typeof segment.geometry === 'string') {
        return decodePolyline(segment.geometry);
      } else if (segment.geometry.coordinates) {
        return segment.geometry.coordinates.map((coord: [number, number]) => [coord[1], coord[0]] as [number, number]);
      }
    }

    console.log('❌ Could not extract route coordinates');
    return [];
  };

  // Get tile layer configuration based on theme
  const getTileLayerConfig = (theme: string) => {
    switch (theme) {
      case 'satellite':
        return {
          url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
          attribution: '&copy; <a href="https://www.esri.com/">Esri</a>, Maxar, Earthstar Geographics',
          maxZoom: 19
        };
      case 'dark':
        return {
          url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
          maxZoom: 19
        };
      case 'terrain':
        return {
          url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
          attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)',
          maxZoom: 17
        };
      case 'standard':
      default:
        return {
          url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        };
    }
  };

  const tileConfig = getTileLayerConfig(mapTheme);

  // Calculate distance between two points in meters
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371000; // Earth's radius in meters
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Filter issues within 500m of current location
  const nearbyIssues = currentLocation ? issues.filter(issue => {
    const distance = calculateDistance(
      currentLocation.lat,
      currentLocation.lng,
      issue.coordinates[0],
      issue.coordinates[1]
    );
    return distance <= 500; // 500 meters
  }) : issues;



  // Map controller component to handle route fitting
  const MapController = () => {
    const map = useMap();

    useEffect(() => {
      if (isNavigating && routeData) {
        const coordinates = getRouteCoordinates();
        if (coordinates.length > 0) {
          // Fit map to show entire route
          const bounds = L.latLngBounds(coordinates);
          map.fitBounds(bounds, { padding: [20, 20] });
          console.log('🗺️ Map fitted to route bounds');
        }
      } else if (currentLocation) {
        // Center on current location when not navigating
        map.setView([currentLocation.lat, currentLocation.lng], 15);
        console.log('🗺️ Map centered on current location');
      }
    }, [map, isNavigating, routeData, currentLocation]);

    return null;
  };

  // Error boundary for map
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (event.message.includes('leaflet') || event.message.includes('map')) {
        console.error('🗺️ Map error detected:', event.message);
        setMapError(event.message);
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  const handleIssueClick = (issue: RoadIssue) => {
    setSelectedIssue(issue);
    if (onIssueClick) {
      onIssueClick(issue);
    }
  };

  // Show error fallback if map fails
  if (mapError) {
    return (
      <div className="relative" style={{ height, width: '100%' }}>
        <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
          <div className="text-center p-8">
            <MapPin className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Map Loading Error</h3>
            <p className="text-gray-600 mb-4">Unable to load the interactive map</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Reload Page
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative" style={{ height, width: '100%' }}>
      <MapContainer
        center={center}
        zoom={zoom}
        style={{ height: '100%', width: '100%', minHeight: '400px' }}
        className="rounded-lg z-0"
        zoomControl={false}
        attributionControl={false}
        whenCreated={(map) => {
          console.log('🗺️ Leaflet map created successfully');
          console.log('📍 Map center:', map.getCenter());
          console.log('🔍 Map zoom:', map.getZoom());
        }}
      >
        <TileLayer
          key={mapTheme} // Force re-render when theme changes
          url={tileConfig.url}
          maxZoom={tileConfig.maxZoom}
          onLoad={() => console.log(`🗺️ ${mapTheme} map tiles loaded successfully`)}
          onError={(error) => console.error(`❌ ${mapTheme} map tile loading error:`, error)}
        />

        {/* Map Controller for automatic view adjustments */}
        <MapController />

        <LocationControl />

        {/* Only show issues within 500m of user location */}
        {nearbyIssues.map((issue, index) => {
          const IconComponent = getIssueIcon(issue);
          console.log(`🗺️ Rendering nearby marker ${index + 1}/${nearbyIssues.length}: ${issue.type} at [${issue.coordinates[0]}, ${issue.coordinates[1]}]`);

          return (
            <Marker
              key={issue.id}
              position={issue.coordinates}
              icon={createCustomIcon(issue)}
              eventHandlers={{
                click: () => handleIssueClick(issue)
              }}
            >
              <Popup>
                <div className="p-2 min-w-[250px]">
                  <div className="flex items-center gap-2 mb-2">
                    <IconComponent className="w-4 h-4" />
                    <h3 className="font-semibold text-sm">{issue.location}</h3>
                  </div>

                  <div className="flex gap-2 mb-2">
                    <Badge className={getSeverityColor(issue.severity) + ' text-white text-xs'}>
                      {issue.severity.toUpperCase()}
                    </Badge>
                    <Badge variant="outline" className={getStatusColor(issue.status) + ' text-xs'}>
                      {issue.status.replace('-', ' ').toUpperCase()}
                    </Badge>
                    {issue.verified && (
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                        VERIFIED
                      </Badge>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mb-2">{issue.description}</p>

                  <div className="text-xs text-gray-500 space-y-1">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>Reported by: {issue.reportedBy || 'Anonymous User'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>
                        {(() => {
                          try {
                            const date = new Date(issue.reportedAt || issue.timestamp);
                            if (isNaN(date.getTime())) {
                              return 'Just now';
                            }
                            const now = new Date();
                            const diffMs = now.getTime() - date.getTime();
                            const diffMins = Math.floor(diffMs / (1000 * 60));
                            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

                            if (diffMins < 1) return 'Just now';
                            if (diffMins < 60) return `${diffMins} min ago`;
                            if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
                            return date.toLocaleDateString();
                          } catch {
                            return 'Just now';
                          }
                        })()}
                      </span>
                    </div>

                    {/* Additional info for police */}
                    {issue.type === 'police' && issue.policeType && (
                      <div className="flex items-center gap-1">
                        <Shield className="w-3 h-3" />
                        <span>Type: {issue.policeType.charAt(0).toUpperCase() + issue.policeType.slice(1)}</span>
                      </div>
                    )}

                    {/* Additional info for cameras */}
                    {issue.type === 'camera' && issue.cameraType && (
                      <div className="flex items-center gap-1">
                        <Camera className="w-3 h-3" />
                        <span>Type: {issue.cameraType.charAt(0).toUpperCase() + issue.cameraType.slice(1)} Camera</span>
                      </div>
                    )}

                    {/* Last seen info for active items */}
                    {issue.lastSeen && (issue.type === 'police' || issue.type === 'camera') && (
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>
                          Last seen: {new Date(issue.lastSeen).toLocaleDateString()} at{' '}
                          {new Date(issue.lastSeen).toLocaleTimeString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </Popup>
            </Marker>
          );
        })}

        {/* Route Visualization */}
        {isNavigating && routeData && (
          <>
            {/* Route Path */}
            {getRouteCoordinates().length > 0 ? (
              <>
                <Polyline
                  positions={getRouteCoordinates()}
                  color="#3B82F6"
                  weight={6}
                  opacity={0.8}
                  dashArray="0"
                />

                {/* Route Path Outline */}
                <Polyline
                  positions={getRouteCoordinates()}
                  color="#1E40AF"
                  weight={8}
                  opacity={0.4}
                  dashArray="0"
                />
              </>
            ) : (
              /* Fallback: Straight line between start and end */
              routeData.startCoordinates && routeData.endCoordinates && (
                <>
                  <Polyline
                    positions={[
                      [routeData.startCoordinates[1], routeData.startCoordinates[0]],
                      [routeData.endCoordinates[1], routeData.endCoordinates[0]]
                    ]}
                    color="#F59E0B"
                    weight={4}
                    opacity={0.8}
                    dashArray="10, 10"
                  />
                </>
              )
            )}

            {/* Start Location Marker */}
            {routeData.startCoordinates && (
              <Marker
                position={[routeData.startCoordinates[1], routeData.startCoordinates[0]]}
                icon={L.divIcon({
                  html: `
                    <div style="
                      width: 30px;
                      height: 30px;
                      background: #10B981;
                      border: 3px solid white;
                      border-radius: 50%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 16px;
                      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                    ">🚀</div>
                  `,
                  className: 'start-location-marker',
                  iconSize: [30, 30],
                  iconAnchor: [15, 15]
                })}
              >
                <Popup>
                  <div className="p-2">
                    <div className="font-semibold text-green-600 mb-1">🚀 Start Location</div>
                    <div className="text-sm text-gray-700">{routeData.startLocation}</div>
                  </div>
                </Popup>
              </Marker>
            )}

            {/* End Location Marker */}
            {routeData.endCoordinates && (
              <Marker
                position={[routeData.endCoordinates[1], routeData.endCoordinates[0]]}
                icon={L.divIcon({
                  html: `
                    <div style="
                      width: 30px;
                      height: 30px;
                      background: #EF4444;
                      border: 3px solid white;
                      border-radius: 50%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 16px;
                      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                    ">🎯</div>
                  `,
                  className: 'end-location-marker',
                  iconSize: [30, 30],
                  iconAnchor: [15, 15]
                })}
              >
                <Popup>
                  <div className="p-2">
                    <div className="font-semibold text-red-600 mb-1">🎯 Destination</div>
                    <div className="text-sm text-gray-700">{routeData.endLocation}</div>
                  </div>
                </Popup>
              </Marker>
            )}
          </>
        )}

        {/* Current Location Marker */}
        {currentLocation && (
          <Marker
            position={[currentLocation.lat, currentLocation.lng]}
            icon={L.divIcon({
              html: `
                <div style="
                  width: 20px;
                  height: 20px;
                  background: #3B82F6;
                  border: 3px solid white;
                  border-radius: 50%;
                  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                  animation: pulse 2s infinite;
                "></div>
                <style>
                  @keyframes pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.2); opacity: 0.7; }
                    100% { transform: scale(1); opacity: 1; }
                  }
                </style>
              `,
              className: 'current-location-marker',
              iconSize: [20, 20],
              iconAnchor: [10, 10]
            })}
          >
            <Popup>
              <div className="p-2">
                <div className="font-semibold text-blue-600 mb-1">📍 Your Location</div>
                <div className="text-xs text-gray-600">
                  {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
                </div>
              </div>
            </Popup>
          </Marker>
        )}

        {/* Navigation Step Markers */}
        {isNavigating && routeData && routeData.segments?.[0]?.steps && (
          <>
            {routeData.segments[0].steps.map((step: any, index: number) => {
              if (!step.maneuver || !step.maneuver.location) return null;

              const [lng, lat] = step.maneuver.location;
              const isCurrentStep = index === navigationStep;

              return (
                <Marker
                  key={`step-${index}`}
                  position={[lat, lng]}
                  icon={L.divIcon({
                    html: `
                      <div style="
                        width: 24px;
                        height: 24px;
                        background: ${isCurrentStep ? '#F59E0B' : '#6B7280'};
                        color: white;
                        border: 2px solid white;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        font-weight: bold;
                        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                        ${isCurrentStep ? 'animation: bounce 1s infinite;' : ''}
                      ">${index + 1}</div>
                      <style>
                        @keyframes bounce {
                          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                          40% { transform: translateY(-5px); }
                          60% { transform: translateY(-3px); }
                        }
                      </style>
                    `,
                    className: 'navigation-step-marker',
                    iconSize: [24, 24],
                    iconAnchor: [12, 12]
                  })}
                >
                  <Popup>
                    <div className="p-2 min-w-[200px]">
                      <div className="font-semibold text-orange-600 mb-1">
                        Step {index + 1} {isCurrentStep ? '(Current)' : ''}
                      </div>
                      <div className="text-sm text-gray-700 mb-2">
                        {step.instruction}
                      </div>
                      <div className="text-xs text-gray-500">
                        Distance: {(step.distance / 1000).toFixed(1)}km
                        <br />
                        Duration: {Math.round(step.duration / 60)} min
                      </div>
                    </div>
                  </Popup>
                </Marker>
              );
            })}
          </>
        )}


      </MapContainer>




    </div>
  );
};

export default InteractiveMap;
