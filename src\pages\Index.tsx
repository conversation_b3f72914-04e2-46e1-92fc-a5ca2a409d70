
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { MapPin, Smartphone, BarChart3, Shield, Clock, Users, Route } from "lucide-react";
import AuthModal from "@/components/auth/AuthModal";
import { useAuth } from "@/contexts/AuthContext";

const Index = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [loginEmail, setLoginEmail] = useState("");
  const [loginPassword, setLoginPassword] = useState("");
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authModalTab, setAuthModalTab] = useState<'signin' | 'signup'>('signin');

  const handleMobileLogin = () => {
    if (isAuthenticated) {
      navigate("/app");
    } else {
      setAuthModalTab('signin');
      setShowAuthModal(true);
    }
  };

  const handleWebLogin = () => {
    if (isAuthenticated) {
      navigate("/app");
    } else {
      setAuthModalTab('signin');
      setShowAuthModal(true);
    }
  };

  const handleCreateAccount = () => {
    setAuthModalTab('signup');
    setShowAuthModal(true);
  };

  const handleAuthSuccess = (isNewUser: boolean) => {
    if (isNewUser) {
      // Redirect new users to main app
      navigate("/app");
    } else {
      // Redirect existing users to main app
      navigate("/app");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-road-charcoal-900 via-road-charcoal-800 to-road-blue-900">
      {/* Header */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-road-yellow-400/10 to-road-blue-400/10"></div>
        <div className="relative container mx-auto px-4 py-8">
          <div className="flex items-center justify-between mb-12">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-road-yellow-400 to-road-yellow-500 rounded-2xl flex items-center justify-center">
                <MapPin className="w-7 h-7 text-road-charcoal-900" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">SmartRoadPulse</h1>
                <p className="text-road-yellow-300 text-sm">Real-Time Road Intelligence</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <Button
                variant="ghost"
                className="text-road-yellow-300 hover:text-road-yellow-400 hover:bg-white/10"
                onClick={handleWebLogin}
              >
                <Route className="w-4 h-4 mr-2" />
                Navigation
              </Button>
              <Button
                variant="ghost"
                className="text-road-blue-300 hover:text-road-blue-400 hover:bg-white/10"
                onClick={handleWebLogin}
              >
                <MapPin className="w-4 h-4 mr-2" />
                Live Map
              </Button>
              <Button
                variant="ghost"
                className="text-red-300 hover:text-red-400 hover:bg-white/10"
                onClick={handleWebLogin}
              >
                <Shield className="w-4 h-4 mr-2" />
                Emergency
              </Button>
              <div className="flex items-center space-x-2 ml-4">
                <Badge variant="outline" className="border-road-yellow-400 text-road-yellow-400">
                  <Clock className="w-3 h-3 mr-1" />
                  Live
                </Badge>
                <Badge variant="outline" className="border-road-blue-400 text-road-blue-400">
                  <Users className="w-3 h-3 mr-1" />
                  24/7 Monitoring
                </Badge>
              </div>
            </div>
          </div>

          {/* Hero Section */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in">
              Monitor Roads.
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-road-yellow-400 to-road-blue-400">
                Save Lives.
              </span>
            </h2>
            <p className="text-xl text-road-charcoal-200 mb-8 max-w-2xl mx-auto animate-slide-up">
              Empowering citizens and authorities with real-time infrastructure intelligence for safer, smarter roads.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in">
              <Button
                size="lg"
                className="bg-road-yellow-500 hover:bg-road-yellow-600 text-road-charcoal-900 font-semibold px-8 py-3 text-lg"
                onClick={() => document.getElementById('login-section')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Get Started
              </Button>
              <Button
                size="lg"
                className="bg-zimbabwe-gold-500 hover:bg-zimbabwe-gold-600 text-white font-semibold px-8 py-3 text-lg"
                onClick={handleWebLogin}
              >
                <Route className="w-5 h-5 mr-2" />
                Launch RoadPulse App
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-road-blue-400 text-road-blue-400 hover:bg-road-blue-400 hover:text-white px-8 py-3 text-lg"
              >
                Watch Demo
              </Button>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-4 gap-6 mb-16">
            <Card className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/15 transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-road-yellow-500 rounded-lg flex items-center justify-center mb-3">
                  <Smartphone className="w-6 h-6 text-road-charcoal-900" />
                </div>
                <CardTitle className="text-white">Mobile Reporting</CardTitle>
                <CardDescription className="text-road-charcoal-300">
                  Quick issue reporting with photo uploads and GPS tracking
                </CardDescription>
              </CardHeader>
            </Card>

            <Card
              className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/20 transition-all duration-300 cursor-pointer group"
              onClick={handleWebLogin}
            >
              <CardHeader>
                <div className="w-12 h-12 bg-zimbabwe-gold-500 rounded-lg flex items-center justify-center mb-3 group-hover:bg-zimbabwe-gold-400 transition-colors">
                  <Route className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white group-hover:text-zimbabwe-gold-300 transition-colors">Smart Route Planning</CardTitle>
                <CardDescription className="text-road-charcoal-300">
                  AI-powered routing with real-time problem detection along your route
                </CardDescription>
                <div className="mt-3">
                  <Button
                    size="sm"
                    className="bg-zimbabwe-gold-500 hover:bg-zimbabwe-gold-600 text-white text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleWebLogin();
                    }}
                  >
                    Try Now →
                  </Button>
                </div>
              </CardHeader>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/15 transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-road-blue-500 rounded-lg flex items-center justify-center mb-3">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white">Real-Time Analytics</CardTitle>
                <CardDescription className="text-road-charcoal-300">
                  Interactive dashboards with heatmaps and trend analysis
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/15 transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-road-yellow-500 rounded-lg flex items-center justify-center mb-3">
                  <Shield className="w-6 h-6 text-road-charcoal-900" />
                </div>
                <CardTitle className="text-white">Authority Dashboard</CardTitle>
                <CardDescription className="text-road-charcoal-300">
                  Comprehensive monitoring tools for infrastructure management
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </div>

      {/* Quick Access Section */}
      <div className="bg-gradient-to-r from-road-blue-900 to-road-charcoal-900 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Try SmartRoadPulse Now</h2>
            <p className="text-road-charcoal-200 text-lg">No registration required for basic features</p>
          </div>

          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/15 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-zimbabwe-gold-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Route className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Route Planning</h3>
                <p className="text-road-charcoal-300 mb-4">Plan safe routes avoiding road issues</p>
                <Button
                  className="w-full bg-zimbabwe-gold-500 hover:bg-zimbabwe-gold-600 text-white"
                  onClick={() => navigate("/route-planning")}
                >
                  Plan Route
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/15 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-road-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Live Map</h3>
                <p className="text-road-charcoal-300 mb-4">View real-time road conditions</p>
                <Button
                  className="w-full bg-road-blue-500 hover:bg-road-blue-600 text-white"
                  onClick={() => navigate("/map")}
                >
                  View Map
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-road-charcoal-600 hover:bg-white/15 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Emergency SOS</h3>
                <p className="text-road-charcoal-300 mb-4">24/7 roadside assistance</p>
                <Button
                  className="w-full bg-red-500 hover:bg-red-600 text-white"
                  onClick={() => navigate("/sos")}
                >
                  Emergency Help
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Login Section */}
      <div id="login-section" className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card className="bg-white/95 backdrop-blur-sm shadow-2xl">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold text-road-charcoal-900">Access SmartRoadPulse</CardTitle>
              <CardDescription>Choose your platform to get started</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="mobile" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="mobile" className="flex items-center gap-2">
                    <Smartphone className="w-4 h-4" />
                    Mobile
                  </TabsTrigger>
                  <TabsTrigger value="web" className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    Web Dashboard
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="mobile" className="space-y-4">
                  <div className="text-center mb-4">
                    <h3 className="font-semibold text-road-charcoal-900 mb-2">Motorist Access</h3>
                    <p className="text-sm text-road-charcoal-600">Report issues and view road conditions</p>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="mobile-email">Email or Phone</Label>
                      <Input id="mobile-email" placeholder="Enter email or phone number" />
                    </div>
                    <div>
                      <Label htmlFor="mobile-password">Password</Label>
                      <Input id="mobile-password" type="password" placeholder="Enter password" />
                    </div>
                    <Button
                      className="w-full bg-road-yellow-500 hover:bg-road-yellow-600 text-road-charcoal-900 font-semibold"
                      onClick={handleMobileLogin}
                    >
                      Access Mobile App
                    </Button>
                    <div className="text-center">
                      <Button variant="link" className="text-road-blue-600 text-sm">
                        USSD Access: *123*456#
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="web" className="space-y-4">
                  <div className="text-center mb-4">
                    <h3 className="font-semibold text-road-charcoal-900 mb-2">Authority Dashboard</h3>
                    <p className="text-sm text-road-charcoal-600">Analytics and management tools</p>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="web-email">Official Email</Label>
                      <Input
                        id="web-email"
                        placeholder="<EMAIL>"
                        value={loginEmail}
                        onChange={(e) => setLoginEmail(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="web-password">Password</Label>
                      <Input
                        id="web-password"
                        type="password"
                        placeholder="Enter secure password"
                        value={loginPassword}
                        onChange={(e) => setLoginPassword(e.target.value)}
                      />
                    </div>
                    <Button
                      className="w-full bg-road-blue-600 hover:bg-road-blue-700 text-white font-semibold"
                      onClick={handleWebLogin}
                    >
                      Access Dashboard
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="mt-6 pt-6 border-t text-center">
                <p className="text-sm text-road-charcoal-600 mb-3">New to SmartRoadPulse?</p>
                <Button
                  variant="outline"
                  className="w-full border-road-charcoal-300"
                  onClick={handleCreateAccount}
                >
                  Create Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-road-charcoal-900 text-road-charcoal-300 py-8">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 SmartRoadPulse. Powering safer roads through intelligent monitoring.</p>
        </div>
      </footer>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab={authModalTab}
        onSuccess={handleAuthSuccess}
      />
    </div>
  );
};

export default Index;
