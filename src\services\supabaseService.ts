/**
 * Supabase Service for RoadPulse
 * Handles all database operations with Supabase
 */

import { createClient } from '@supabase/supabase-js';
import { RoadIssue } from '../components/InteractiveMap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SOSHelper } from '../types/sos';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 Supabase configuration:', {
  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'NOT SET',
  keyPrefix: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NOT SET'
});

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables:', {
    VITE_SUPABASE_URL: !!supabaseUrl,
    VITE_SUPABASE_ANON_KEY: !!supabaseAnonKey
  });
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
interface DatabaseIncident {
  id: string;
  type: string;
  subtype?: string;
  location: any; // PostGIS geography type
  address?: string;
  severity: string;
  description?: string;
  reported_by?: string;
  verified_count: number;
  confidence_score: number;
  is_verified: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // User info from join
  reported_by_username?: string;
  reporter_trust_score?: number;
  longitude?: number;
  latitude?: number;
}

interface DatabaseSOSRequest {
  id: string;
  user_id: string;
  sos_type: string;
  urgency_level: string;
  location: any;
  address?: string;
  description: string;
  help_needed?: string;
  estimated_helpers: number;
  status: string;
  created_at: string;
  // User info from join
  username?: string;
  full_name?: string;
  phone?: string;
  trust_score?: number;
  longitude?: number;
  latitude?: number;
}

class SupabaseService {
  /**
   * Get all active road incidents
   */
  async getActiveIncidents(): Promise<RoadIssue[]> {
    console.log('🗺️ Fetching active incidents from Supabase...');

    try {
      const { data, error } = await supabase
        .rpc('get_active_incidents_with_coords');

      if (error) {
        console.error('❌ Error fetching incidents:', error);
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw new Error(`Supabase error: ${error.message || 'Unknown error'}`);
      }

      console.log(`✅ Fetched ${data?.length || 0} incidents from database`);

      return this.transformIncidentsToRoadIssues(data || []);
    } catch (error) {
      console.error('❌ Failed to fetch incidents:', error);
      throw error;
    }
  }

  /**
   * Get active SOS requests
   */
  async getActiveSOSRequests(): Promise<SOSAlert[]> {
    console.log('🆘 Fetching active SOS requests from Supabase...');

    try {
      const { data, error } = await supabase
        .rpc('get_active_sos_with_coords');

      if (error) {
        console.error('❌ Error fetching SOS requests:', error);
        throw error;
      }

      console.log(`✅ Fetched ${data?.length || 0} SOS requests from database`);

      return this.transformSOSRequestsToAlerts(data || []);
    } catch (error) {
      console.error('❌ Failed to fetch SOS requests:', error);
      throw error;
    }
  }

  /**
   * Create a new road incident
   */
  async createIncident(incident: {
    type: string;
    subtype?: string;
    location: [number, number]; // [lat, lng]
    address?: string;
    severity: string;
    description?: string;
  }): Promise<RoadIssue> {
    console.log('📍 Creating new incident:', incident);

    try {
      // Get current user or use anonymous
      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('road_incidents')
        .insert({
          type: incident.type,
          subtype: incident.subtype,
          location: `POINT(${incident.location[1]} ${incident.location[0]})`, // PostGIS format: lng lat
          address: incident.address,
          severity: incident.severity,
          description: incident.description,
          reported_by: user?.id || 'anonymous', // Use user ID or anonymous
          reporter_name: user?.user_metadata?.name || 'Anonymous User'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating incident:', error);
        throw error;
      }

      console.log('✅ Created incident:', data);
      
      // Transform to RoadIssue format
      return this.transformIncidentToRoadIssue(data);
    } catch (error) {
      console.error('❌ Failed to create incident:', error);
      throw error;
    }
  }

  /**
   * Create a new SOS request
   */
  async createSOSRequest(sosRequest: {
    sos_type: string;
    urgency_level: string;
    location: [number, number]; // [lat, lng]
    address?: string;
    description: string;
    help_needed?: string;
    estimated_helpers?: number;
  }): Promise<SOSAlert> {
    console.log('🆘 Creating new SOS request:', sosRequest);

    try {
      // Get current user or use anonymous
      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('sos_requests')
        .insert({
          sos_type: sosRequest.sos_type,
          urgency_level: sosRequest.urgency_level,
          location: `POINT(${sosRequest.location[1]} ${sosRequest.location[0]})`,
          address: sosRequest.address,
          description: sosRequest.description,
          help_needed: sosRequest.help_needed,
          estimated_helpers: sosRequest.estimated_helpers || 1,
          user_id: user?.id || 'anonymous', // Use user ID or anonymous
          user_name: user?.user_metadata?.name || 'Anonymous User'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating SOS request:', error);
        throw error;
      }

      console.log('✅ Created SOS request:', data);
      
      // Transform to SOSAlert format
      return this.transformSOSRequestToAlert(data);
    } catch (error) {
      console.error('❌ Failed to create SOS request:', error);
      throw error;
    }
  }

  /**
   * Transform database incidents to RoadIssue format
   */
  private transformIncidentsToRoadIssues(incidents: DatabaseIncident[]): RoadIssue[] {
    return incidents.map(incident => this.transformIncidentToRoadIssue(incident));
  }

  /**
   * Transform single database incident to RoadIssue format
   */
  private transformIncidentToRoadIssue(incident: any): RoadIssue {
    return {
      id: incident.id,
      type: incident.type as RoadIssue['type'],
      location: incident.address || 'Unknown location',
      coordinates: [incident.latitude || 0, incident.longitude || 0],
      severity: incident.severity as RoadIssue['severity'],
      description: incident.description || '',
      reportedBy: incident.username || 'Unknown user',
      reportedAt: incident.created_at,
      status: incident.is_active ? 'active' : 'resolved',
      verified: incident.is_verified,
      // Additional properties for specific types
      ...(incident.type === 'police' && {
        isActive: incident.is_active,
        policeType: incident.subtype,
        lastSeen: incident.updated_at,
      }),
      ...(incident.type === 'camera' && {
        isActive: incident.is_active,
        cameraType: incident.subtype,
      }),
    };
  }

  /**
   * Transform database SOS requests to SOSAlert format
   */
  private transformSOSRequestsToAlerts(sosRequests: DatabaseSOSRequest[]): SOSAlert[] {
    return sosRequests.map(sos => this.transformSOSRequestToAlert(sos));
  }

  /**
   * Transform single database SOS request to SOSAlert format
   */
  private transformSOSRequestToAlert(sos: any): SOSAlert {
    return {
      id: sos.id,
      userId: sos.user_id,
      location: {
        lat: sos.latitude || 0,
        lng: sos.longitude || 0,
        accuracy: 10, // Default accuracy
        address: sos.address,
      },
      timestamp: new Date(sos.created_at),
      status: sos.status as SOSAlert['status'],
      riskScore: 0.3, // Default risk score
      fraudProbability: 0.1, // Default fraud probability
      description: sos.description,
      category: sos.sos_type as SOSAlert['category'],
      helpers: [], // Will be populated separately
      escalationHistory: [],
      // Additional SOS-specific properties
      urgencyLevel: sos.urgency_level,
      helpNeeded: sos.help_needed,
      estimatedHelpers: sos.estimated_helpers,
      userProfile: {
        name: sos.full_name || sos.username || 'Unknown user',
        phone: sos.phone || '',
        rating: 4.5, // Default rating
        trustScore: sos.trust_score || 50,
        profileImage: `https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face`,
        vehicleInfo: 'Vehicle info not available',
      },
    };
  }

  /**
   * Subscribe to real-time changes
   */
  subscribeToIncidents(callback: (incident: RoadIssue) => void) {
    console.log('🔄 Setting up real-time incident subscription...');
    
    return supabase
      .channel('road-incidents')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'road_incidents'
      }, (payload) => {
        console.log('🆕 New incident received:', payload.new);
        const incident = this.transformIncidentToRoadIssue(payload.new as DatabaseIncident);
        callback(incident);
      })
      .subscribe();
  }

  /**
   * Subscribe to real-time SOS changes
   */
  subscribeToSOSRequests(callback: (sosAlert: SOSAlert) => void) {
    console.log('🔄 Setting up real-time SOS subscription...');
    
    return supabase
      .channel('sos-requests')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'sos_requests'
      }, (payload) => {
        console.log('🆕 New SOS request received:', payload.new);
        const sosAlert = this.transformSOSRequestToAlert(payload.new as DatabaseSOSRequest);
        callback(sosAlert);
      })
      .subscribe();
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();
