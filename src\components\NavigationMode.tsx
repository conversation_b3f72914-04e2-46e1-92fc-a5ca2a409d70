import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Navigation,
  Volume2,
  VolumeX,
  Phone,
  Settings,
  ArrowUp,
  ArrowRight,
  ArrowLeft,
  RotateCcw,
  MapPin,
  AlertTriangle,
  Clock,
  Gauge,
  X,
  Maximize,
  Minimize,
  Shield,
  Camera,
  Eye,
  Heart,
  Wrench,
  Fuel,
  Star,
  User,
  MessageCircle,
  Timer
} from 'lucide-react';
import { RoadIssue } from './InteractiveMap';

interface NavigationModeProps {
  routeCoordinates: [number, number][];
  issues: RoadIssue[];
  onExit: () => void;
  destination: string;
  totalDistance: number;
  totalDuration: number;
  isFullScreen?: boolean;
  onToggleFullScreen?: () => void;
}

interface NavigationState {
  currentPosition: [number, number];
  currentSegment: number;
  distanceRemaining: number;
  timeRemaining: number;
  currentSpeed: number;
  nextTurn: string;
  nextTurnDistance: number;
  isNavigating: boolean;
}

// Car marker component with optimized rendering
const CarMarker: React.FC<{ position: [number, number]; heading: number }> = ({ position, heading }) => {
  const map = useMap();
  const markerRef = useRef<L.Marker | null>(null);
  const lastPositionRef = useRef<[number, number] | null>(null);

  useEffect(() => {
    // Only update map view if position changed significantly (reduce flickering)
    if (!lastPositionRef.current ||
        Math.abs(lastPositionRef.current[0] - position[0]) > 0.0001 ||
        Math.abs(lastPositionRef.current[1] - position[1]) > 0.0001) {

      map.setView(position, 18, {
        animate: true,
        duration: 0.8,
        easeLinearity: 0.1
      });
      lastPositionRef.current = position;
    }
  }, [position, map]);

  // Create sleek, modern car icon
  const carIcon = L.divIcon({
    html: `
      <div style="transform: rotate(${heading}deg); transition: transform 0.8s ease-out;">
        <svg width="28" height="28" viewBox="0 0 28 28" style="filter: drop-shadow(0 2px 6px rgba(0,0,0,0.3));">
          <!-- Car body -->
          <path d="M6 18 L6 14 Q6 12 8 12 L20 12 Q22 12 22 14 L22 18 Q22 19 21 19 L7 19 Q6 19 6 18 Z"
                fill="#2563eb" stroke="#ffffff" stroke-width="1.5"/>

          <!-- Car roof -->
          <path d="M8 12 L8 8 Q8 6 10 6 L18 6 Q20 6 20 8 L20 12"
                fill="#1d4ed8" stroke="#ffffff" stroke-width="1"/>

          <!-- Windshield -->
          <path d="M8 8 L10 6 L18 6 L20 8 L18 10 L10 10 Z"
                fill="rgba(255,255,255,0.3)" stroke="#1d4ed8" stroke-width="0.5"/>

          <!-- Headlights -->
          <circle cx="10" cy="5" r="1.5" fill="#fbbf24" stroke="#ffffff" stroke-width="0.5"/>
          <circle cx="18" cy="5" r="1.5" fill="#fbbf24" stroke="#ffffff" stroke-width="0.5"/>

          <!-- Wheels -->
          <circle cx="9" cy="20" r="2.5" fill="#374151" stroke="#ffffff" stroke-width="1"/>
          <circle cx="19" cy="20" r="2.5" fill="#374151" stroke="#ffffff" stroke-width="1"/>
          <circle cx="9" cy="20" r="1" fill="#6b7280"/>
          <circle cx="19" cy="20" r="1" fill="#6b7280"/>

          <!-- Direction arrow -->
          <polygon points="14,2 16,4 15,4 15,6 13,6 13,4 12,4" fill="#10b981" stroke="#ffffff" stroke-width="0.5"/>
        </svg>
      </div>
    `,
    className: 'sleek-car-marker',
    iconSize: [28, 28],
    iconAnchor: [14, 14]
  });

  return (
    <Marker
      ref={markerRef}
      position={position}
      icon={carIcon}
    />
  );
};

// Route line component with orange styling and optimized rendering
const RoutePolyline: React.FC<{
  coordinates: [number, number][];
  currentSegment: number;
  completedColor?: string;
  remainingColor?: string;
}> = React.memo(({
  coordinates,
  currentSegment,
  completedColor = '#10b981',
  remainingColor = '#f97316'
}) => {
  const completedRoute = coordinates.slice(0, Math.max(1, currentSegment + 1));
  const remainingRoute = coordinates.slice(currentSegment);

  return (
    <>
      {/* Completed route (green) */}
      {completedRoute.length > 1 && (
        <Polyline
          key={`completed-${currentSegment}`}
          positions={completedRoute}
          pathOptions={{
            color: completedColor,
            weight: 6,
            opacity: 0.9,
            lineCap: 'round',
            lineJoin: 'round'
          }}
        />
      )}
      {/* Remaining route (orange) */}
      {remainingRoute.length > 1 && (
        <Polyline
          key={`remaining-${currentSegment}`}
          positions={remainingRoute}
          pathOptions={{
            color: remainingColor,
            weight: 8,
            opacity: 0.9,
            lineCap: 'round',
            lineJoin: 'round'
          }}
        />
      )}
    </>
  );
});

const NavigationMode: React.FC<NavigationModeProps> = ({
  routeCoordinates,
  issues,
  onExit,
  destination,
  totalDistance,
  totalDuration,
  isFullScreen = true,
  onToggleFullScreen
}) => {
  const [navState, setNavState] = useState<NavigationState>({
    currentPosition: routeCoordinates[0],
    currentSegment: 0,
    distanceRemaining: totalDistance,
    timeRemaining: totalDuration,
    currentSpeed: 0,
    nextTurn: 'Continue straight',
    nextTurnDistance: 500,
    isNavigating: false
  });

  const [isUsingGPS, setIsUsingGPS] = useState(false);
  const watchIdRef = useRef<number | null>(null);

  const [isMuted, setIsMuted] = useState(false);
  const [showSpeedometer, setShowSpeedometer] = useState(true);
  const [currentHeading, setCurrentHeading] = useState(0);
  const [selectedSOS, setSelectedSOS] = useState<RoadIssue | null>(null);
  const [showSOSDetails, setShowSOSDetails] = useState(false);
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Simulate navigation movement with optimized updates
  useEffect(() => {
    if (!navState.isNavigating) return;

    intervalRef.current = setInterval(() => {
      setNavState(prev => {
        const nextSegment = Math.min(prev.currentSegment + 1, routeCoordinates.length - 1);

        if (nextSegment >= routeCoordinates.length - 1) {
          // Navigation complete
          return { ...prev, isNavigating: false, currentSpeed: 0 };
        }

        const newPosition = routeCoordinates[nextSegment];
        const prevPosition = routeCoordinates[prev.currentSegment];

        // Calculate heading only if position changed significantly
        let heading = currentHeading;
        if (Math.abs(newPosition[0] - prevPosition[0]) > 0.0001 ||
            Math.abs(newPosition[1] - prevPosition[1]) > 0.0001) {
          const deltaLat = newPosition[0] - prevPosition[0];
          const deltaLng = newPosition[1] - prevPosition[1];
          heading = Math.atan2(deltaLng, deltaLat) * (180 / Math.PI);
          setCurrentHeading(heading);
        }

        // Calculate remaining distance (simplified)
        const remainingSegments = routeCoordinates.length - nextSegment;
        const distanceRemaining = (remainingSegments / routeCoordinates.length) * totalDistance;
        const timeRemaining = (distanceRemaining / totalDistance) * totalDuration;

        // Simulate speed variation with smoother changes
        const speedVariation = (Math.sin(Date.now() / 10000) + 1) * 10; // Smooth variation
        const speed = 45 + speedVariation; // 35-55 km/h

        // Generate next turn instruction
        const nextTurn = generateTurnInstruction(nextSegment, routeCoordinates);

        return {
          ...prev,
          currentPosition: newPosition,
          currentSegment: nextSegment,
          distanceRemaining,
          timeRemaining,
          currentSpeed: speed,
          nextTurn,
          nextTurnDistance: Math.max(50, Math.random() * 800)
        };
      });
    }, 3000); // Slower updates to reduce flickering

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [navState.isNavigating, routeCoordinates, totalDistance, totalDuration, currentHeading]);

  const generateTurnInstruction = (segment: number, coordinates: [number, number][]): string => {
    if (segment >= coordinates.length - 2) {
      return 'Destination ahead';
    }

    if (segment >= coordinates.length - 5) {
      return 'Approaching destination';
    }

    // Calculate turn angle based on route geometry
    const current = coordinates[segment];
    const next = coordinates[Math.min(segment + 3, coordinates.length - 1)];
    const future = coordinates[Math.min(segment + 6, coordinates.length - 1)];

    if (!current || !next || !future) {
      return 'Continue straight';
    }

    // Calculate bearing changes
    const bearing1 = calculateBearing(current, next);
    const bearing2 = calculateBearing(next, future);
    const turnAngle = normalizeBearing(bearing2 - bearing1);

    // Generate instruction based on turn angle
    if (Math.abs(turnAngle) < 15) {
      return 'Continue straight';
    } else if (turnAngle > 15 && turnAngle < 45) {
      return 'Keep right';
    } else if (turnAngle < -15 && turnAngle > -45) {
      return 'Keep left';
    } else if (turnAngle >= 45 && turnAngle < 135) {
      return 'Turn right';
    } else if (turnAngle <= -45 && turnAngle > -135) {
      return 'Turn left';
    } else if (Math.abs(turnAngle) >= 135) {
      return 'Make a U-turn';
    }

    return 'Continue straight';
  };

  // Calculate bearing between two points
  const calculateBearing = (from: [number, number], to: [number, number]): number => {
    const deltaLng = to[1] - from[1];
    const deltaLat = to[0] - from[0];
    return Math.atan2(deltaLng, deltaLat) * (180 / Math.PI);
  };

  // Normalize bearing to -180 to 180 range
  const normalizeBearing = (bearing: number): number => {
    while (bearing > 180) bearing -= 360;
    while (bearing < -180) bearing += 360;
    return bearing;
  };

  // Start real-time GPS tracking
  const startGPSTracking = () => {
    if (!navigator.geolocation) {
      console.warn('⚠️ Geolocation not supported, using simulated navigation');
      setIsUsingGPS(false);
      return;
    }

    console.log('📍 Starting real-time GPS tracking...');
    setIsUsingGPS(true);

    watchIdRef.current = navigator.geolocation.watchPosition(
      (position) => {
        const newPosition: [number, number] = [
          position.coords.latitude,
          position.coords.longitude
        ];

        const speed = position.coords.speed ? position.coords.speed * 3.6 : 0; // Convert m/s to km/h
        const heading = position.coords.heading || 0;

        console.log(`📍 GPS Update: [${newPosition[0]}, ${newPosition[1]}] Speed: ${speed.toFixed(1)}km/h`);

        // Find closest point on route
        const closestSegment = findClosestRouteSegment(newPosition, routeCoordinates);

        // Calculate remaining distance and time
        const remainingSegments = routeCoordinates.length - closestSegment;
        const distanceRemaining = (remainingSegments / routeCoordinates.length) * totalDistance;
        const timeRemaining = speed > 0 ? (distanceRemaining / speed) * 3600 : totalDuration;

        setNavState(prev => ({
          ...prev,
          currentPosition: newPosition,
          currentSegment: closestSegment,
          currentSpeed: speed,
          distanceRemaining,
          timeRemaining,
          nextTurn: generateTurnInstruction(closestSegment, routeCoordinates),
          nextTurnDistance: Math.max(50, Math.random() * 500)
        }));

        setCurrentHeading(heading);
      },
      (error) => {
        console.error('❌ GPS tracking error:', error);
        setIsUsingGPS(false);
        // Fall back to simulated navigation
        startSimulatedNavigation();
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 1000
      }
    );
  };

  // Stop GPS tracking
  const stopGPSTracking = () => {
    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
    }
    setIsUsingGPS(false);
  };

  // Find closest point on route to current GPS position
  const findClosestRouteSegment = (currentPos: [number, number], route: [number, number][]): number => {
    let closestIndex = 0;
    let minDistance = Infinity;

    for (let i = 0; i < route.length; i++) {
      const distance = Math.sqrt(
        Math.pow(currentPos[0] - route[i][0], 2) +
        Math.pow(currentPos[1] - route[i][1], 2)
      );

      if (distance < minDistance) {
        minDistance = distance;
        closestIndex = i;
      }
    }

    return closestIndex;
  };

  // Simulated navigation for testing/fallback
  const startSimulatedNavigation = () => {
    setNavState(prev => ({ ...prev, isNavigating: true, currentSpeed: 45 }));
  };

  const startNavigation = () => {
    console.log('🚀 Starting navigation...');

    // Try to start GPS tracking first
    if (navigator.geolocation) {
      startGPSTracking();
    } else {
      startSimulatedNavigation();
    }

    setNavState(prev => ({ ...prev, isNavigating: true }));
  };

  const stopNavigation = () => {
    console.log('🛑 Stopping navigation...');
    stopGPSTracking();
    setNavState(prev => ({ ...prev, isNavigating: false, currentSpeed: 0 }));
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  // Cleanup GPS tracking on component unmount
  useEffect(() => {
    return () => {
      stopGPSTracking();
    };
  }, []);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatDistance = (meters: number) => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    }
    return `${Math.round(meters)} m`;
  };

  const dismissAlert = (alertType: string) => {
    setDismissedAlerts(prev => new Set([...prev, alertType]));
  };

  // Create navigation icon for issues
  const createNavigationIcon = (issue: RoadIssue) => {
    const getIconColor = (type: string, urgencyLevel?: string) => {
      if (type === 'police') return '#3b82f6'; // Blue
      if (type === 'camera') return '#8b5cf6'; // Purple
      if (type === 'pothole') return '#f97316'; // Orange
      if (type === 'construction') return '#eab308'; // Yellow
      if (type === 'accident') return '#ef4444'; // Red
      if (type === 'sos') {
        switch (urgencyLevel) {
          case 'critical': return '#dc2626'; // Bright red for critical
          case 'high': return '#ea580c'; // Orange-red for high
          case 'medium': return '#d97706'; // Orange for medium
          case 'low': return '#16a34a'; // Green for low
          default: return '#dc2626'; // Default to critical red
        }
      }
      return '#6b7280'; // Gray default
    };

    const getIconSymbol = (type: string, sosType?: string) => {
      if (type === 'police') return '👮';
      if (type === 'camera') return '📹';
      if (type === 'pothole') return '🕳️';
      if (type === 'construction') return '🚧';
      if (type === 'accident') return '⚠️';
      if (type === 'sos') {
        switch (sosType) {
          case 'medical': return '🚑';
          case 'breakdown': return '🔧';
          case 'fuel': return '⛽';
          case 'tire': return '🛞';
          case 'accident': return '🚗';
          case 'security': return '🚨';
          default: return '🆘';
        }
      }
      return '!';
    };

    const color = getIconColor(issue.type, issue.urgencyLevel);
    const symbol = getIconSymbol(issue.type, issue.sosType);
    const isActive = issue.isActive && (issue.type === 'police' || issue.type === 'camera' || issue.type === 'sos');

    return L.divIcon({
      html: `
        <div style="
          background: ${color};
          color: white;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          border: 3px solid white;
          box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          ${isActive ? 'animation: pulse 2s infinite;' : ''}
        ">${symbol}</div>
      `,
      className: 'custom-navigation-marker',
      iconSize: [32, 32],
      iconAnchor: [16, 16]
    });
  };

  // Categorize issues for live updates
  const categorizeIssues = () => {
    if (!issues || !Array.isArray(issues)) {
      return {
        nearby: [],
        police: [],
        cameras: [],
        roadIssues: [],
        all: []
      };
    }

    const issuesWithDistance = issues.map(issue => ({
      ...issue,
      distance: Math.sqrt(
        Math.pow(issue.coordinates[0] - navState.currentPosition[0], 2) +
        Math.pow(issue.coordinates[1] - navState.currentPosition[1], 2)
      )
    }));

    return {
      nearby: issuesWithDistance.filter(issue => issue.distance < 0.02), // ~2km
      police: issuesWithDistance.filter(issue => issue.type === 'police' && issue.isActive),
      cameras: issuesWithDistance.filter(issue => issue.type === 'camera' && issue.isActive),
      sos: issuesWithDistance.filter(issue => issue.type === 'sos' && issue.isActive && issue.distance < 0.018), // ~2km for SOS
      roadIssues: issuesWithDistance.filter(issue =>
        ['pothole', 'construction', 'flooding', 'accident'].includes(issue.type)
      ),
      all: issuesWithDistance
    };
  };

  const categorizedIssues = categorizeIssues();

  return (
    <div className={`${isFullScreen ? 'w-full h-full' : 'w-full h-full'} bg-black flex flex-col navigation-ui relative rounded-lg`}>
      {/* Compact Top Status Bar */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-2 flex items-center justify-between relative z-[1001] shrink-0">
        <div className="flex items-center gap-3">
          <Button
            onClick={onExit}
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-700 p-1"
          >
            <X className="w-4 h-4" />
          </Button>
          <div className="flex items-center gap-2">
            <Navigation className="w-4 h-4 text-orange-400" />
            <span className="font-semibold text-sm">Navigation</span>
            {isUsingGPS && (
              <div className="flex items-center gap-1 bg-green-600 px-2 py-1 rounded text-xs">
                <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                GPS
              </div>
            )}
            {!isUsingGPS && navState.isNavigating && (
              <div className="flex items-center gap-1 bg-yellow-600 px-2 py-1 rounded text-xs">
                <div className="w-2 h-2 bg-yellow-300 rounded-full"></div>
                SIM
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-3 text-xs">
          <div className="text-center">
            <div className="text-gray-300">ETA</div>
            <div className="font-bold">{formatTime(navState.timeRemaining)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-300">Distance</div>
            <div className="font-bold">{formatDistance(navState.distanceRemaining)}</div>
          </div>
          <Button
            onClick={() => setIsMuted(!isMuted)}
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-700 p-1"
          >
            {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
          </Button>
          {onToggleFullScreen && (
            <Button
              onClick={onToggleFullScreen}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-gray-700 p-1"
              title={isFullScreen ? "Exit Fullscreen" : "Enter Fullscreen"}
            >
              {isFullScreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </Button>
          )}
        </div>
      </div>

      {/* Main Navigation Display */}
      <div className="flex-1 relative">
        {/* Map */}
        <MapContainer
          center={routeCoordinates[0]} // Use initial position to prevent re-renders
          zoom={18}
          style={{ height: '100%', width: '100%' }}
          zoomControl={false}
          attributionControl={false}
          preferCanvas={true} // Use canvas for better performance
          updateWhenIdle={true} // Reduce updates
          updateWhenZooming={false} // Prevent updates during zoom
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />

          {/* Route */}
          <RoutePolyline
            coordinates={routeCoordinates}
            currentSegment={navState.currentSegment}
          />

          {/* Car marker */}
          <CarMarker
            position={navState.currentPosition}
            heading={currentHeading}
          />

          {/* All Issues with Clean Icons - NO POPUPS for cleaner navigation */}
          {categorizedIssues.all.map(issue => (
            <Marker
              key={issue.id}
              position={issue.coordinates}
              icon={createNavigationIcon(issue)}
              eventHandlers={issue.type === 'sos' ? {
                click: () => {
                  setSelectedSOS(issue);
                  setShowSOSDetails(true);
                }
              } : undefined}
            >
              {/* Simple popup with minimal info only for non-SOS issues */}
              {issue.type !== 'sos' && (
                <Popup>
                  <div className="p-2 max-w-[200px]">
                    <div className="flex items-center gap-2 mb-1">
                      {issue.type === 'police' && <Shield className="w-4 h-4 text-blue-500" />}
                      {issue.type === 'camera' && <Camera className="w-4 h-4 text-purple-500" />}
                      {!['police', 'camera', 'sos'].includes(issue.type) && <AlertTriangle className="w-4 h-4 text-orange-500" />}
                      <span className="font-semibold text-sm">{issue.location}</span>
                    </div>
                    <p className="text-xs text-gray-600">{issue.description}</p>
                    <Badge className={`${
                      issue.type === 'police' ? 'bg-blue-500' :
                      issue.type === 'camera' ? 'bg-purple-500' :
                      'bg-orange-500'
                    } text-white text-xs mt-1`}>
                      {issue.type.toUpperCase()}
                    </Badge>
                  </div>
                </Popup>
              )}
            </Marker>
          ))}
        </MapContainer>

        {/* Turn Instruction Overlay - ALWAYS VISIBLE */}
        <div
          className="absolute top-4 left-4 right-4 z-[9999]"
          style={{
            zIndex: 9999,
            position: 'absolute',
            pointerEvents: 'auto'
          }}
        >
          <Card className="bg-white shadow-2xl border-2 border-orange-500">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                  <ArrowUp className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <div className="text-xl font-bold text-gray-900">{navState.nextTurn}</div>
                  <div className="text-base text-gray-600">in {formatDistance(navState.nextTurnDistance)}</div>
                  <div className="text-sm text-gray-500">to {destination}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Speed and Controls - ALWAYS VISIBLE */}
        {showSpeedometer && (
          <div
            className="absolute bottom-4 left-4 z-[9999]"
            style={{
              zIndex: 9999,
              position: 'absolute',
              pointerEvents: 'auto'
            }}
          >
            <Card className="bg-black text-white border-2 border-orange-500">
              <CardContent className="p-4 text-center">
                <div className="text-3xl font-bold">{Math.round(navState.currentSpeed)}</div>
                <div className="text-sm text-gray-300">km/h</div>
                <div className="flex items-center justify-center gap-1 mt-2">
                  <Gauge className="w-4 h-4" />
                  <span className="text-xs">Speed</span>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Live Updates Panel */}
        <div className="absolute top-20 right-4 max-w-xs space-y-2 z-[9999]">
          {/* Police Alerts */}
          {categorizedIssues.police.length > 0 && !dismissedAlerts.has('police') && (
            <Card className="bg-blue-500/90 backdrop-blur-sm text-white">
              <CardContent className="p-2">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-1">
                    <Shield className="w-4 h-4" />
                    <span className="font-semibold text-sm">Police Nearby</span>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <button
                    onClick={() => dismissAlert('police')}
                    className="text-blue-200 hover:text-white p-1"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                {categorizedIssues.police.slice(0, 2).map(issue => (
                  <div key={issue.id} className="text-xs mb-1">
                    <div className="font-medium">{issue.policeType?.toUpperCase()}: {issue.location}</div>
                    {issue.lastSeen && (
                      <div className="text-blue-200">
                        Last seen: {new Date(issue.lastSeen).toLocaleTimeString()}
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Camera Alerts */}
          {categorizedIssues.cameras.length > 0 && !dismissedAlerts.has('cameras') && (
            <Card className="bg-purple-500/90 backdrop-blur-sm text-white">
              <CardContent className="p-2">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-1">
                    <Camera className="w-4 h-4" />
                    <span className="font-semibold text-sm">Cameras Ahead</span>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <button
                    onClick={() => dismissAlert('cameras')}
                    className="text-purple-200 hover:text-white p-1"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                {categorizedIssues.cameras.slice(0, 2).map(issue => (
                  <div key={issue.id} className="text-xs mb-1">
                    <div className="font-medium">{issue.cameraType?.toUpperCase()}: {issue.location}</div>
                    <div className="text-purple-200">{issue.description}</div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* SOS Help Requests - Priority Display */}
          {categorizedIssues.sos && categorizedIssues.sos.length > 0 && !dismissedAlerts.has('sos') && (
            <Card className="bg-gradient-to-r from-red-600 to-red-500 backdrop-blur-sm text-white border-2 border-red-300 animate-pulse">
              <CardContent className="p-2">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-white rounded-full animate-ping"></div>
                    <span className="font-bold text-sm">🆘 HELP NEEDED</span>
                    <div className="w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
                  </div>
                  <button
                    onClick={() => dismissAlert('sos')}
                    className="text-red-200 hover:text-white p-1"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                {categorizedIssues.sos.slice(0, 2).map(issue => (
                  <div key={issue.id} className="text-xs mb-1 bg-red-700/50 p-1 rounded">
                    <div className="font-medium flex items-center gap-1">
                      {issue.sosType === 'medical' && '🚑'}
                      {issue.sosType === 'breakdown' && '🔧'}
                      {issue.sosType === 'fuel' && '⛽'}
                      {issue.sosType === 'tire' && '🛞'}
                      {!['medical', 'breakdown', 'fuel', 'tire'].includes(issue.sosType || '') && '🆘'}
                      <span className="uppercase">{issue.sosType}: {issue.location}</span>
                    </div>
                    <div className="text-red-100 flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>{issue.userProfile?.name}</span>
                      <Star className="w-3 h-3 text-yellow-300" />
                      <span>{issue.userProfile?.rating}</span>
                    </div>
                    <div className="text-red-200 text-xs">
                      {issue.urgencyLevel?.toUpperCase()} • {issue.responseTime}min ETA
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Road Issues Alerts */}
          {categorizedIssues.nearby.filter(i => !['police', 'camera', 'sos'].includes(i.type)).length > 0 && !dismissedAlerts.has('roadIssues') && (
            <Card className="bg-red-500/90 backdrop-blur-sm text-white">
              <CardContent className="p-2">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-1">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="font-semibold text-sm">Road Issues</span>
                  </div>
                  <button
                    onClick={() => dismissAlert('roadIssues')}
                    className="text-red-200 hover:text-white p-1"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                {categorizedIssues.nearby
                  .filter(i => !['police', 'camera', 'sos'].includes(i.type))
                  .slice(0, 2)
                  .map(issue => (
                    <div key={issue.id} className="text-xs mb-1">
                      <div className="font-medium">{issue.type.toUpperCase()}: {issue.location}</div>
                      <div className="text-red-200">{issue.severity?.toUpperCase()} severity</div>
                    </div>
                  ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Navigation Controls - ALWAYS VISIBLE */}
        <div
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-[9999]"
          style={{
            zIndex: 9999,
            position: 'absolute',
            pointerEvents: 'auto'
          }}
        >
          <div className="flex items-center gap-2">
            {!navState.isNavigating ? (
              <Button
                onClick={startNavigation}
                className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 text-lg font-semibold border-2 border-white"
              >
                <Navigation className="w-5 h-5 mr-2" />
                Start Navigation
              </Button>
            ) : (
              <Button
                onClick={stopNavigation}
                variant="outline"
                className="bg-white hover:bg-gray-100 text-gray-900 px-6 py-3 border-2 border-orange-500"
              >
                Stop
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Info Bar with Live Updates */}
      <div className="bg-gradient-to-r from-orange-600 to-orange-500 text-white p-3 relative z-[1001] shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span className="text-sm">Via fastest route</span>
            </div>

            {/* Live Status Indicators */}
            <div className="flex items-center gap-2">
              {categorizedIssues.police.length > 0 && (
                <Badge className="bg-blue-600 text-white text-xs flex items-center gap-1">
                  <Shield className="w-3 h-3" />
                  {categorizedIssues.police.length} Police
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                </Badge>
              )}

              {categorizedIssues.cameras.length > 0 && (
                <Badge className="bg-purple-600 text-white text-xs flex items-center gap-1">
                  <Camera className="w-3 h-3" />
                  {categorizedIssues.cameras.length} Camera{categorizedIssues.cameras.length > 1 ? 's' : ''}
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                </Badge>
              )}

              {categorizedIssues.sos && categorizedIssues.sos.length > 0 && (
                <Badge className="bg-gradient-to-r from-red-600 to-red-500 text-white text-xs flex items-center gap-1 animate-pulse border border-red-300">
                  <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
                  <span>🆘</span>
                  {categorizedIssues.sos.length} Help Request{categorizedIssues.sos.length > 1 ? 's' : ''}
                  <div className="w-1.5 h-1.5 bg-yellow-300 rounded-full animate-pulse"></div>
                </Badge>
              )}

              {categorizedIssues.nearby.filter(i => !['police', 'camera', 'sos'].includes(i.type)).length > 0 && (
                <Badge className="bg-red-600 text-white text-xs flex items-center gap-1">
                  <AlertTriangle className="w-3 h-3" />
                  {categorizedIssues.nearby.filter(i => !['police', 'camera', 'sos'].includes(i.type)).length} Issue{categorizedIssues.nearby.filter(i => !['police', 'camera', 'sos'].includes(i.type)).length > 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>LIVE</span>
            </div>
            <Clock className="w-4 h-4" />
            <span>🇿🇼 SmartRoadPulse Navigation</span>
          </div>
        </div>
      </div>

      {/* SOS Details Modal - Clean and Focused */}
      {showSOSDetails && selectedSOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-[2000] flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full animate-pulse ${
                    selectedSOS.urgencyLevel === 'critical' ? 'bg-red-500' :
                    selectedSOS.urgencyLevel === 'high' ? 'bg-orange-500' :
                    selectedSOS.urgencyLevel === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`}></div>
                  <span className="font-bold text-lg text-red-600">🆘 HELP NEEDED</span>
                </div>
                <button
                  onClick={() => setShowSOSDetails(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* User Profile Section */}
              {selectedSOS.userProfile && (
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg mb-4">
                  <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                    {selectedSOS.userProfile.profileImage ? (
                      <img
                        src={selectedSOS.userProfile.profileImage}
                        alt={selectedSOS.userProfile.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <User className="w-6 h-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 truncate">{selectedSOS.userProfile.name}</h3>
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        <span className="text-xs text-gray-600">{selectedSOS.userProfile.rating}</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 mb-1">
                      Trust Score: {selectedSOS.userProfile.trustScore}%
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedSOS.userProfile.vehicleInfo}
                    </div>
                  </div>
                </div>
              )}

              {/* Help Details */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center gap-2">
                  {selectedSOS.sosType === 'medical' && <Heart className="w-4 h-4 text-red-500" />}
                  {selectedSOS.sosType === 'breakdown' && <Wrench className="w-4 h-4 text-orange-500" />}
                  {selectedSOS.sosType === 'fuel' && <Fuel className="w-4 h-4 text-blue-500" />}
                  {selectedSOS.sosType === 'tire' && <AlertTriangle className="w-4 h-4 text-yellow-500" />}
                  {!['medical', 'breakdown', 'fuel', 'tire'].includes(selectedSOS.sosType || '') && <AlertTriangle className="w-4 h-4 text-gray-500" />}
                  <span className="font-medium">
                    {selectedSOS.sosType?.charAt(0).toUpperCase() + selectedSOS.sosType?.slice(1)} Emergency
                  </span>
                  <Badge className={`${
                    selectedSOS.urgencyLevel === 'critical' ? 'bg-red-500' :
                    selectedSOS.urgencyLevel === 'high' ? 'bg-orange-500' :
                    selectedSOS.urgencyLevel === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  } text-white text-xs`}>
                    {selectedSOS.urgencyLevel?.toUpperCase()}
                  </Badge>
                </div>

                <div className="bg-blue-50 p-3 rounded">
                  <strong className="text-sm">Help Needed:</strong>
                  <p className="text-sm mt-1">{selectedSOS.helpNeeded}</p>
                </div>

                <div className="text-sm text-gray-600">{selectedSOS.description}</div>
              </div>

              {/* Response Info */}
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4 p-3 bg-gray-50 rounded">
                <div className="flex items-center gap-1">
                  <Timer className="w-4 h-4" />
                  <span>ETA: {selectedSOS.responseTime}min</span>
                </div>
                <div className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  <span>{selectedSOS.estimatedHelpers} helpers nearby</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button className="flex-1 bg-green-600 hover:bg-green-700 text-white">
                  <Phone className="w-4 h-4 mr-2" />
                  Call Now
                </Button>
                <Button variant="outline" className="flex-1">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Message
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NavigationMode;
